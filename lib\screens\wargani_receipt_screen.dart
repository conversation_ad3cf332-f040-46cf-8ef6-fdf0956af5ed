import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:wargani/l10n/app_localizations.dart';
import 'package:wargani/models/wargani_model.dart';
import 'package:wargani/utils/hive_helper.dart';
import 'package:wargani/utils/pdf_generator.dart';
import 'package:wargani/widgets/footer.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';

// Minor change to trigger re-save and pick up new localizations
class WarganiReceiptScreen extends StatefulWidget {
  const WarganiReceiptScreen({super.key});

  @override
  _WarganiReceiptScreenState createState() => _WarganiReceiptScreenState();
}

class _WarganiReceiptScreenState extends State<WarganiReceiptScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _amountController = TextEditingController();
  final _receiptNoController = TextEditingController();
  final _prefixController = TextEditingController();
  final _mobileNoController = TextEditingController();
  final _registrationNoController = TextEditingController();
  final _amountInWordsController = TextEditingController();
  DateTime _selectedDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    _receiptNoController.text =
        (HiveHelper.getWarganiBox().length + 1).toString();
  }

  void _clearForm() {
    _formKey.currentState?.reset();
    _nameController.clear();
    _amountController.clear();
    _prefixController.clear();
    _mobileNoController.clear();
    _registrationNoController.clear();
    _amountInWordsController.clear();
    setState(() {
      _receiptNoController.text =
          (HiveHelper.getWarganiBox().length + 1).toString();
      _selectedDate = DateTime.now();
    });
  }

  Future<void> _saveReceipt() async {
    if (_formKey.currentState!.validate()) {
      final wargani = Wargani(
        receiptNo: int.parse(_receiptNoController.text),
        name: _nameController.text,
        amount: double.parse(_amountController.text),
        date: _selectedDate,
        prefix: _prefixController.text,
        mobileNo: _mobileNoController.text.isNotEmpty
            ? _mobileNoController.text
            : null,
        registrationNo: _registrationNoController.text,
        amountInWords: _amountInWordsController.text,
      );
      await HiveHelper.getWarganiBox().add(wargani);
      _clearForm();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text(
                AppLocalizations.of(context)!.pdfGeneratedSuccessfully)),
      );
    }
  }

  Future<void> _downloadPdf(Wargani wargani) async {
    final pdfPath = await PdfGenerator.generate(
        context, wargani, HiveHelper.getUsersBox().getAt(0)?.name);
    if (pdfPath != null) {
      await Share.shareXFiles([XFile(pdfPath)]);
    }
  }

  Future<void> _sharePdf(Wargani wargani) async {
    if (wargani.mobileNo != null && wargani.mobileNo!.isNotEmpty) {
      final url =
          "https://wa.me/${wargani.mobileNo}?text=Thank you for your donation!";
      if (await canLaunchUrl(Uri.parse(url))) {
        await launchUrl(Uri.parse(url));
      }
    } else {
      final pdfPath = await PdfGenerator.generate(
          context, wargani, HiveHelper.getUsersBox().getAt(0)?.name);
      if (pdfPath != null) {
        await Share.shareXFiles([XFile(pdfPath)]);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
          return <Widget>[
            SliverAppBar(
              expandedHeight: 200.0,
              floating: false,
              pinned: true,
              flexibleSpace: FlexibleSpaceBar(
                title: Text(localizations.warganiReceipt),
                background: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Form(
                    key: _formKey,
                    child: ListView(
                      children: [
                        TextFormField(
                          controller: _receiptNoController,
                          decoration: InputDecoration(
                              labelText: localizations.receiptNo),
                          keyboardType: TextInputType.number,
                          readOnly: true,
                        ),
                        TextFormField(
                          controller: _nameController,
                          decoration:
                              InputDecoration(labelText: localizations.name),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return localizations.pleaseEnterName;
                            }
                            return null;
                          },
                        ),
                        TextFormField(
                          controller: _amountController,
                          decoration: InputDecoration(
                              labelText: localizations.amount),
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return localizations.pleaseEnterAmount;
                            }
                            if (double.tryParse(value) == null) {
                              return localizations.pleaseEnterValidAmount;
                            }
                            return null;
                          },
                        ),
                        TextFormField(
                          controller: _amountInWordsController,
                          decoration: InputDecoration(
                              labelText: localizations.amountInWords),
                        ),
                        TextFormField(
                          controller: _prefixController,
                          decoration: InputDecoration(
                              labelText: localizations.prefix),
                        ),
                        TextFormField(
                          controller: _mobileNoController,
                          decoration: InputDecoration(
                              labelText: localizations.mobileNo),
                          keyboardType: TextInputType.phone,
                        ),
                        TextFormField(
                          controller: _registrationNoController,
                          decoration: InputDecoration(
                              labelText: localizations.registrationNo),
                        ),
                        ListTile(
                          title: Text(
                              '${localizations.date}: ${DateFormat('dd/MM/yyyy').format(_selectedDate)}'),
                          trailing: const Icon(Icons.calendar_today),
                          onTap: () async {
                            final DateTime? picked = await showDatePicker(
                              context: context,
                              initialDate: _selectedDate,
                              firstDate: DateTime(2000),
                              lastDate: DateTime(2101),
                            );
                            if (picked != null && picked != _selectedDate) {
                              setState(() {
                                _selectedDate = picked;
                              });
                            }
                          },
                        ),
                        ElevatedButton(
                          onPressed: _saveReceipt,
                          child: Text(localizations.saveReceipt),
                        ),
                        ElevatedButton(
                          onPressed: _clearForm,
                          child: Text(localizations.clearForm),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ];
        },
        body: ValueListenableBuilder(
          valueListenable: HiveHelper.getWarganiBox().listenable(),
          builder: (context, Box<Wargani> box, _) {
            final receipts = box.values.toList().cast<Wargani>();
            return ListView.builder(
              itemCount: receipts.length,
              itemBuilder: (context, index) {
                final receipt = receipts[index];
                return Card(
                  margin:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: ListTile(
                    title: Text(receipt.name),
                    subtitle: Text(
                        '${localizations.receiptNo}: ${receipt.receiptNo} - ₹${receipt.amount}'),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          icon: const Icon(Icons.download),
                          onPressed: () => _downloadPdf(receipt),
                        ),
                        IconButton(
                          icon: const Icon(Icons.share),
                          onPressed: () => _sharePdf(receipt),
                        ),
                      ],
                    ),
                  ),
                );
              },
            );
          },
        ),
      ),
      bottomSheet: const Footer(),
    );
  }
}
