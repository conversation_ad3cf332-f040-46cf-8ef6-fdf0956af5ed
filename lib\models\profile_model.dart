import 'package:hive/hive.dart';
import 'dart:typed_data'; // Import Uint8List

part 'profile_model.g.dart';

@HiveType(typeId: 0)
class Profile extends HiveObject {
  @HiveField(0)
  String mandalName;

  @HiveField(1)
  String address;

  @HiveField(2)
  Uint8List? logoBytes; // Change from logoPath to logoBytes

  @HiveField(3)
  String currentYear;

  @HiveField(4)
  String? mandalRegistrationNo;

  Profile({
    required this.mandalName,
    required this.address,
    this.logoBytes, // Update constructor parameter
    required this.currentYear,
    this.mandalRegistrationNo,
  });
}
