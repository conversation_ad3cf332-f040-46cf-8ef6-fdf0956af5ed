class NumberToWords {
  // English number to words
  static String convertToEnglish(int number) {
    if (number == 0) return 'Zero';
    
    List<String> ones = [
      '', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine',
      'Ten', 'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen',
      'Seventeen', 'Eighteen', 'Nineteen'
    ];
    
    List<String> tens = [
      '', '', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety'
    ];
    
    String result = '';
    
    if (number >= 10000000) { // Crores
      int crores = number ~/ 10000000;
      result += convertToEnglish(crores) + ' Crore ';
      number %= 10000000;
    }
    
    if (number >= 100000) { // Lakhs
      int lakhs = number ~/ 100000;
      result += convertToEnglish(lakhs) + ' Lakh ';
      number %= 100000;
    }
    
    if (number >= 1000) { // Thousands
      int thousands = number ~/ 1000;
      result += convertToEnglish(thousands) + ' Thousand ';
      number %= 1000;
    }
    
    if (number >= 100) { // Hundreds
      result += ones[number ~/ 100] + ' Hundred ';
      number %= 100;
    }
    
    if (number >= 20) {
      result += tens[number ~/ 10] + ' ';
      number %= 10;
    }
    
    if (number > 0) {
      result += ones[number] + ' ';
    }
    
    return result.trim();
  }
  
  // Marathi number to words
  static String convertToMarathi(int number) {
    if (number == 0) return 'शून्य';
    
    List<String> ones = [
      '', 'एक', 'दोन', 'तीन', 'चार', 'पाच', 'सहा', 'सात', 'आठ', 'नऊ',
      'दहा', 'अकरा', 'बारा', 'तेरा', 'चौदा', 'पंधरा', 'सोळा',
      'सतरा', 'अठरा', 'एकोणीस'
    ];
    
    List<String> tens = [
      '', '', 'वीस', 'तीस', 'चाळीस', 'पन्नास', 'साठ', 'सत्तर', 'ऐंशी', 'नव्वद'
    ];
    
    String result = '';
    
    if (number >= 10000000) { // कोटी
      int crores = number ~/ 10000000;
      result += convertToMarathi(crores) + ' कोटी ';
      number %= 10000000;
    }
    
    if (number >= 100000) { // लाख
      int lakhs = number ~/ 100000;
      result += convertToMarathi(lakhs) + ' लाख ';
      number %= 100000;
    }
    
    if (number >= 1000) { // हजार
      int thousands = number ~/ 1000;
      result += convertToMarathi(thousands) + ' हजार ';
      number %= 1000;
    }
    
    if (number >= 100) { // शंभर
      result += ones[number ~/ 100] + ' शंभर ';
      number %= 100;
    }
    
    if (number >= 20) {
      result += tens[number ~/ 10] + ' ';
      number %= 10;
    }
    
    if (number > 0) {
      result += ones[number] + ' ';
    }
    
    return result.trim();
  }
  
  // Hindi number to words
  static String convertToHindi(int number) {
    if (number == 0) return 'शून्य';
    
    List<String> ones = [
      '', 'एक', 'दो', 'तीन', 'चार', 'पांच', 'छह', 'सात', 'आठ', 'नौ',
      'दस', 'ग्यारह', 'बारह', 'तेरह', 'चौदह', 'पंद्रह', 'सोलह',
      'सत्रह', 'अठारह', 'उन्नीस'
    ];
    
    List<String> tens = [
      '', '', 'बीस', 'तीस', 'चालीस', 'पचास', 'साठ', 'सत्तर', 'अस्सी', 'नब्बे'
    ];
    
    String result = '';
    
    if (number >= 10000000) { // करोड़
      int crores = number ~/ 10000000;
      result += convertToHindi(crores) + ' करोड़ ';
      number %= 10000000;
    }
    
    if (number >= 100000) { // लाख
      int lakhs = number ~/ 100000;
      result += convertToHindi(lakhs) + ' लाख ';
      number %= 100000;
    }
    
    if (number >= 1000) { // हजार
      int thousands = number ~/ 1000;
      result += convertToHindi(thousands) + ' हजार ';
      number %= 1000;
    }
    
    if (number >= 100) { // सौ
      result += ones[number ~/ 100] + ' सौ ';
      number %= 100;
    }
    
    if (number >= 20) {
      result += tens[number ~/ 10] + ' ';
      number %= 10;
    }
    
    if (number > 0) {
      result += ones[number] + ' ';
    }
    
    return result.trim();
  }
  
  // Convert amount to words with currency
  static String convertAmountToWords(double amount, String language) {
    int rupees = amount.floor();
    int paise = ((amount - rupees) * 100).round();
    
    String result = '';
    
    switch (language) {
      case 'mr': // Marathi
        if (rupees > 0) {
          result += convertToMarathi(rupees) + ' रुपये';
        }
        if (paise > 0) {
          if (result.isNotEmpty) result += ' आणि ';
          result += convertToMarathi(paise) + ' पैसे';
        }
        if (result.isEmpty) result = 'शून्य रुपये';
        result += ' फक्त';
        break;
        
      case 'hi': // Hindi
        if (rupees > 0) {
          result += convertToHindi(rupees) + ' रुपये';
        }
        if (paise > 0) {
          if (result.isNotEmpty) result += ' और ';
          result += convertToHindi(paise) + ' पैसे';
        }
        if (result.isEmpty) result = 'शून्य रुपये';
        result += ' मात्र';
        break;
        
      default: // English
        if (rupees > 0) {
          result += convertToEnglish(rupees) + ' Rupees';
        }
        if (paise > 0) {
          if (result.isNotEmpty) result += ' and ';
          result += convertToEnglish(paise) + ' Paise';
        }
        if (result.isEmpty) result = 'Zero Rupees';
        result += ' Only';
        break;
    }
    
    return result;
  }
}
