import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:wargani_marathi/models/profile_model.dart';
import 'package:wargani_marathi/models/wargani_model.dart';
import 'package:wargani_marathi/screens/home_screen.dart';
import 'package:wargani_marathi/utils/hive_helper.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Hive initialization
  await Hive.initFlutter();

  // Register adapters
  Hive.registerAdapter(ProfileAdapter());
  Hive.registerAdapter(WarganiAdapter());

  // Open boxes
  await HiveHelper.openBoxes();

  runApp(const WarganiMarathiApp());
}

class WarganiMarathiApp extends StatelessWidget {
  const WarganiMarathiApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'वारगणी मराठी',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: Colors.orange,
        primaryColor: const Color(0xFFFF8C00),
        fontFamily: 'Hind',
        appBarTheme: const AppBarTheme(
          backgroundColor: Color(0xFFFF8C00),
          foregroundColor: Colors.white,
          elevation: 0,
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFFFF8C00),
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
        cardTheme: CardTheme(
          elevation: 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
      home: const HomeScreen(),
    );
  }
}


