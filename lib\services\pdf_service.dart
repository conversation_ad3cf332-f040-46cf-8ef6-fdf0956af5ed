import 'dart:typed_data';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:wargani/models/wargani_model.dart';
import 'package:wargani/models/profile_model.dart';
import 'package:wargani/utils/hive_helper.dart';
import 'dart:html' as html;
import 'package:flutter/foundation.dart';

/// Native PDF generation service for Marathi and Hindi receipts
class PDFService {
  
  /// Generate native Marathi/Hindi receipt PDF
  static Future<Uint8List> generateWarganiReceipt(Wargani wargani) async {
    final pdf = pw.Document();
    final profile = HiveHelper.getProfileBox().get('profile');
    final currentLanguage = HiveHelper.getLocale() ?? 'en';
    
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(40),
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // Header with <PERSON><PERSON><PERSON> blessing
              _buildHeader(currentLanguage),
              pw.SizedBox(height: 20),
              
              // Organization details
              _buildOrganizationDetails(profile, currentLanguage),
              pw.SizedBox(height: 20),
              
              // Receipt details
              _buildReceiptDetails(wargani, currentLanguage),
              pw.SizedBox(height: 30),
              
              // Amount section
              _buildAmountSection(wargani, currentLanguage),
              pw.SizedBox(height: 40),
              
              // Footer
              _buildFooter(currentLanguage),
            ],
          );
        },
      ),
    );
    
    return pdf.save();
  }
  
  /// Build header with Ganesh blessing
  static pw.Widget _buildHeader(String language) {
    String headerText;
    
    switch (language) {
      case 'mr':
        headerText = '|| श्री गणेश प्रसन्न ||';
        break;
      case 'hi':
        headerText = '|| श्री गणेश प्रसन्न ||';
        break;
      default:
        headerText = '|| Shri Ganesh Prasanna ||';
    }
    
    return pw.Center(
      child: pw.Text(
        headerText,
        style: pw.TextStyle(
          fontSize: 18,
          fontWeight: pw.FontWeight.bold,
        ),
      ),
    );
  }
  
  /// Build organization details
  static pw.Widget _buildOrganizationDetails(Profile? profile, String language) {
    if (profile == null) {
      return pw.Container();
    }
    
    switch (language) {
      case 'mr':
        return pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text('रजी नं.: ${profile.mandalRegistrationNo ?? 'N/A'}'),
            pw.Text('संस्था नाव: ${profile.mandalName}'),
            pw.Text('पत्ता: ${profile.address}'),
          ],
        );
      case 'hi':
        return pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text('पंजीकरण क्रमांक: ${profile.mandalRegistrationNo ?? 'N/A'}'),
            pw.Text('संस्था का नाम: ${profile.mandalName}'),
            pw.Text('पता: ${profile.address}'),
          ],
        );
      default:
        return pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text('Registration No.: ${profile.mandalRegistrationNo ?? 'N/A'}'),
            pw.Text('Organization: ${profile.mandalName}'),
            pw.Text('Address: ${profile.address}'),
          ],
        );
    }
  }
  
  /// Build receipt details
  static pw.Widget _buildReceiptDetails(Wargani wargani, String language) {
    final dateStr = '${wargani.date.day.toString().padLeft(2, '0')}/'
                   '${wargani.date.month.toString().padLeft(2, '0')}/'
                   '${wargani.date.year}';
    
    switch (language) {
      case 'mr':
        return pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text('पावती क्रमांक: ${wargani.receiptNo}'),
            pw.Text('दिनांक: $dateStr'),
            pw.SizedBox(height: 10),
            pw.Text('${wargani.prefix ?? 'श्री'} ${wargani.name} यांच्याकडून'),
          ],
        );
      case 'hi':
        return pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text('रसीद क्रमांक: ${wargani.receiptNo}'),
            pw.Text('दिनांक: $dateStr'),
            pw.SizedBox(height: 10),
            pw.Text('${wargani.prefix ?? 'श्री'} ${wargani.name} से प्राप्त'),
          ],
        );
      default:
        return pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text('Receipt No.: ${wargani.receiptNo}'),
            pw.Text('Date: $dateStr'),
            pw.SizedBox(height: 10),
            pw.Text('Received from: ${wargani.prefix ?? 'Mr.'} ${wargani.name}'),
          ],
        );
    }
  }
  
  /// Build amount section
  static pw.Widget _buildAmountSection(Wargani wargani, String language) {
    final amountInWords = _convertAmountToWords(wargani.amount, language);
    
    switch (language) {
      case 'mr':
        return pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text(
              'रोख रक्कम प्राप्त झाली: ₹ ${wargani.amount.toStringAsFixed(0)}',
              style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
            ),
            pw.SizedBox(height: 10),
            pw.Text(
              'अक्षरी रक्कम: $amountInWords रुपये फक्त',
              style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
            ),
          ],
        );
      case 'hi':
        return pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text(
              'प्राप्त राशि: ₹ ${wargani.amount.toStringAsFixed(0)}',
              style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
            ),
            pw.SizedBox(height: 10),
            pw.Text(
              'अक्षरों में राशि: $amountInWords रुपये मात्र',
              style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
            ),
          ],
        );
      default:
        return pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text(
              'Amount Received: ₹ ${wargani.amount.toStringAsFixed(0)}',
              style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
            ),
            pw.SizedBox(height: 10),
            pw.Text(
              'Amount in Words: $amountInWords Rupees Only',
              style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
            ),
          ],
        );
    }
  }
  
  /// Build footer
  static pw.Widget _buildFooter(String language) {
    switch (language) {
      case 'mr':
        return pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text(
              'धन्यवाद…!',
              style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
            ),
            pw.SizedBox(height: 20),
            pw.Text('स्वाक्षरी: __________________'),
          ],
        );
      case 'hi':
        return pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text(
              'धन्यवाद…!',
              style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
            ),
            pw.SizedBox(height: 20),
            pw.Text('हस्ताक्षर: __________________'),
          ],
        );
      default:
        return pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text(
              'Thank You!',
              style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
            ),
            pw.SizedBox(height: 20),
            pw.Text('Signature: __________________'),
          ],
        );
    }
  }
  
  /// Convert amount to words in native language
  static String _convertAmountToWords(double amount, String language) {
    final intAmount = amount.toInt();
    
    switch (language) {
      case 'mr':
        return _convertToMarathiWords(intAmount);
      case 'hi':
        return _convertToHindiWords(intAmount);
      default:
        return _convertToEnglishWords(intAmount);
    }
  }
  
  /// Convert to Marathi words
  static String _convertToMarathiWords(int amount) {
    if (amount == 0) return 'शून्य';
    
    final ones = ['', 'एक', 'दोन', 'तीन', 'चार', 'पाच', 'सहा', 'सात', 'आठ', 'नऊ'];
    final teens = ['दहा', 'अकरा', 'बारा', 'तेरा', 'चौदा', 'पंधरा', 'सोळा', 'सतरा', 'अठरा', 'एकोणीस'];
    final tens = ['', '', 'वीस', 'तीस', 'चाळीस', 'पन्नास', 'साठ', 'सत्तर', 'ऐंशी', 'नव्वद'];
    
    String result = '';
    
    if (amount >= 10000000) { // Crore
      final crores = amount ~/ 10000000;
      result += '${_convertToMarathiWords(crores)} कोटी ';
      amount %= 10000000;
    }
    
    if (amount >= 100000) { // Lakh
      final lakhs = amount ~/ 100000;
      result += '${_convertToMarathiWords(lakhs)} लाख ';
      amount %= 100000;
    }
    
    if (amount >= 1000) { // Thousand
      final thousands = amount ~/ 1000;
      result += '${_convertToMarathiWords(thousands)} हजार ';
      amount %= 1000;
    }
    
    if (amount >= 100) { // Hundred
      final hundreds = amount ~/ 100;
      result += '${ones[hundreds]} शे ';
      amount %= 100;
    }
    
    if (amount >= 20) {
      result += '${tens[amount ~/ 10]}';
      if (amount % 10 != 0) {
        result += '${ones[amount % 10]}';
      }
    } else if (amount >= 10) {
      result += teens[amount - 10];
    } else if (amount > 0) {
      result += ones[amount];
    }
    
    return result.trim();
  }
  
  /// Convert to Hindi words
  static String _convertToHindiWords(int amount) {
    if (amount == 0) return 'शून्य';
    
    final ones = ['', 'एक', 'दो', 'तीन', 'चार', 'पाँच', 'छह', 'सात', 'आठ', 'नौ'];
    final teens = ['दस', 'ग्यारह', 'बारह', 'तेरह', 'चौदह', 'पंद्रह', 'सोलह', 'सत्रह', 'अठारह', 'उन्नीस'];
    final tens = ['', '', 'बीस', 'तीस', 'चालीस', 'पचास', 'साठ', 'सत्तर', 'अस्सी', 'नब्बे'];
    
    String result = '';
    
    if (amount >= 10000000) { // Crore
      final crores = amount ~/ 10000000;
      result += '${_convertToHindiWords(crores)} करोड़ ';
      amount %= 10000000;
    }
    
    if (amount >= 100000) { // Lakh
      final lakhs = amount ~/ 100000;
      result += '${_convertToHindiWords(lakhs)} लाख ';
      amount %= 100000;
    }
    
    if (amount >= 1000) { // Thousand
      final thousands = amount ~/ 1000;
      result += '${_convertToHindiWords(thousands)} हजार ';
      amount %= 1000;
    }
    
    if (amount >= 100) { // Hundred
      final hundreds = amount ~/ 100;
      result += '${ones[hundreds]} सौ ';
      amount %= 100;
    }
    
    if (amount >= 20) {
      result += '${tens[amount ~/ 10]}';
      if (amount % 10 != 0) {
        result += ' ${ones[amount % 10]}';
      }
    } else if (amount >= 10) {
      result += teens[amount - 10];
    } else if (amount > 0) {
      result += ones[amount];
    }
    
    return result.trim();
  }
  
  /// Convert to English words (fallback)
  static String _convertToEnglishWords(int amount) {
    if (amount == 0) return 'Zero';
    
    final ones = ['', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine'];
    final teens = ['Ten', 'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen', 'Seventeen', 'Eighteen', 'Nineteen'];
    final tens = ['', '', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety'];
    
    String result = '';
    
    if (amount >= 10000000) { // Crore
      final crores = amount ~/ 10000000;
      result += '${_convertToEnglishWords(crores)} Crore ';
      amount %= 10000000;
    }
    
    if (amount >= 100000) { // Lakh
      final lakhs = amount ~/ 100000;
      result += '${_convertToEnglishWords(lakhs)} Lakh ';
      amount %= 100000;
    }
    
    if (amount >= 1000) { // Thousand
      final thousands = amount ~/ 1000;
      result += '${_convertToEnglishWords(thousands)} Thousand ';
      amount %= 1000;
    }
    
    if (amount >= 100) { // Hundred
      final hundreds = amount ~/ 100;
      result += '${ones[hundreds]} Hundred ';
      amount %= 100;
    }
    
    if (amount >= 20) {
      result += '${tens[amount ~/ 10]}';
      if (amount % 10 != 0) {
        result += ' ${ones[amount % 10]}';
      }
    } else if (amount >= 10) {
      result += teens[amount - 10];
    } else if (amount > 0) {
      result += ones[amount];
    }
    
    return result.trim();
  }

  /// Download PDF for web platform
  static void downloadPDFForWeb(Uint8List pdfBytes, String filename) {
    if (kIsWeb) {
      // Create blob and download for web
      final blob = html.Blob([pdfBytes], 'application/pdf');
      final url = html.Url.createObjectUrlFromBlob(blob);
      final anchor = html.AnchorElement(href: url)
        ..setAttribute('download', filename)
        ..click();
      html.Url.revokeObjectUrl(url);
    }
  }
}
