import 'dart:io';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:wargani/models/profile_model.dart';
import 'package:wargani/models/wargani_model.dart';
import 'package:wargani/utils/hive_helper.dart';
import 'package:flutter/material.dart';
import 'package:wargani/models/donation_model.dart';
import 'package:wargani/models/expense_model.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wargani/l10n/app_localizations.dart';
import 'package:wargani/utils/number_to_words.dart';

class PdfGenerator {
  // Helper method to get amount in words based on language
  static String _getAmountInWords(double amount, bool isMarathi, bool isHindi) {
    int amountInt = amount.toInt();
    String words = _convertNumberToWords(amountInt, isMarathi, isHindi);

    if (isMarathi) {
      return '$words रुपये फक्त';
    } else if (isHindi) {
      return '$words रुपये मात्र';
    } else {
      return '$words Rupees Only';
    }
  }

  // Convert number to words - Enhanced version
  static String _convertNumberToWords(int number, bool isMarathi, bool isHindi) {
    if (number == 0) {
      return isMarathi ? 'शून्य' : isHindi ? 'शून्य' : 'Zero';
    }

    // Enhanced number to words conversion
    if (number >= 10000000) { // 1 crore and above
      int crores = number ~/ 10000000;
      int remainder = number % 10000000;
      String croreWord = isMarathi ? 'कोटी' : isHindi ? 'करोड़' : 'Crore';
      String result = _convertNumberToWords(crores, isMarathi, isHindi) + ' $croreWord';
      if (remainder > 0) {
        result += ' ' + _convertNumberToWords(remainder, isMarathi, isHindi);
      }
      return result;
    } else if (number >= 100000) { // 1 lakh and above
      int lakhs = number ~/ 100000;
      int remainder = number % 100000;
      String lakhWord = isMarathi ? 'लाख' : isHindi ? 'लाख' : 'Lakh';
      String result = _convertNumberToWords(lakhs, isMarathi, isHindi) + ' $lakhWord';
      if (remainder > 0) {
        result += ' ' + _convertNumberToWords(remainder, isMarathi, isHindi);
      }
      return result;
    } else if (number >= 1000) { // 1 thousand and above
      int thousands = number ~/ 1000;
      int remainder = number % 1000;
      String thousandWord = isMarathi ? 'हजार' : isHindi ? 'हजार' : 'Thousand';
      String result = _convertNumberToWords(thousands, isMarathi, isHindi) + ' $thousandWord';
      if (remainder > 0) {
        result += ' ' + _convertNumberToWords(remainder, isMarathi, isHindi);
      }
      return result;
    } else if (number >= 100) { // 100 to 999
      int hundreds = number ~/ 100;
      int remainder = number % 100;
      String hundredWord = isMarathi ? 'शे' : isHindi ? 'सौ' : 'Hundred';
      String result = _convertBasicNumber(hundreds, isMarathi, isHindi) + ' $hundredWord';
      if (remainder > 0) {
        result += ' ' + _convertBasicNumber(remainder, isMarathi, isHindi);
      }
      return result;
    } else {
      return _convertBasicNumber(number, isMarathi, isHindi);
    }
  }

  // Convert basic numbers (1-99) with proper Marathi
  static String _convertBasicNumber(int number, bool isMarathi, bool isHindi) {
    List<String> ones = isMarathi
        ? ['', 'एक', 'दोन', 'तीन', 'चार', 'पाच', 'सहा', 'सात', 'आठ', 'नव', 'दहा', 'अकरा', 'बारा', 'तेरा', 'चौदा', 'पंधरा', 'सोळा', 'सतरा', 'अठरा', 'एकोणीस']
        : isHindi
        ? ['', 'एक', 'दो', 'तीन', 'चार', 'पांच', 'छह', 'सात', 'आठ', 'नौ', 'दस', 'ग्यारह', 'बारह', 'तेरह', 'चौदह', 'पंद्रह', 'सोलह', 'सत्रह', 'अठारह', 'उन्नीस']
        : ['', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine', 'Ten', 'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen', 'Seventeen', 'Eighteen', 'Nineteen'];

    List<String> tens = isMarathi
        ? ['', '', 'वीस', 'तीस', 'चाळीस', 'पन्नास', 'साठ', 'सत्तर', 'ऐंशी', 'नव्वद']
        : isHindi
        ? ['', '', 'बीस', 'तीस', 'चालीस', 'पचास', 'साठ', 'सत्तर', 'अस्सी', 'नब्बे']
        : ['', '', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety'];

    // Special cases for Marathi numbers
    if (isMarathi) {
      Map<int, String> specialMarathi = {
        21: 'एकवीस', 22: 'बावीस', 23: 'तेवीस', 24: 'चोवीस', 25: 'पंचवीस', 26: 'सव्वीस', 27: 'सत्तावीस', 28: 'अठ्ठावीस', 29: 'एकोणतीस',
        31: 'एकतीस', 32: 'बत्तीस', 33: 'तेहतीस', 34: 'चौतीस', 35: 'पस्तीस', 36: 'छत्तीस', 37: 'सदतीस', 38: 'अडतीस', 39: 'एकोणचाळीस',
        41: 'एकेचाळीस', 42: 'बेचाळीस', 43: 'त्रेचाळीस', 44: 'चव्वेचाळीस', 45: 'पंचेचाळीस', 46: 'सेहेचाळीस', 47: 'सत्तेचाळीस', 48: 'अठ्ठेचाळीस', 49: 'एकोणपन्नास',
        51: 'एक्कावन्न', 52: 'बावन्न', 53: 'त्रेपन्न', 54: 'चोपन्न', 55: 'पंचावन्न', 56: 'छप्पन्न', 57: 'सत्तावन्न', 58: 'अठ्ठावन्न', 59: 'एकोणसाठ',
        61: 'एकसष्ठ', 62: 'बासष्ठ', 63: 'त्रेसष्ठ', 64: 'चौसष्ठ', 65: 'पासष्ठ', 66: 'सहासष्ठ', 67: 'सदुसष्ठ', 68: 'अडुसष्ठ', 69: 'एकोणसत्तर',
        71: 'एक्काहत्तर', 72: 'बाहत्तर', 73: 'त्र्याहत्तर', 74: 'चौर्‍याहत्तर', 75: 'पंच्याहत्तर', 76: 'शहात्तर', 77: 'सत्याहत्तर', 78: 'अठ्ठ्याहत्तर', 79: 'एकोणऐंशी',
        81: 'एक्क्याऐंशी', 82: 'ब्याऐंशी', 83: 'त्र्याऐंशी', 84: 'चौर्‍याऐंशी', 85: 'पंच्याऐंशी', 86: 'शहाऐंशी', 87: 'सत्याऐंशी', 88: 'अठ्ठ्याऐंशी', 89: 'एकोणनव्वद',
        91: 'एक्क्याण्णव', 92: 'ब्याण्णव', 93: 'त्र्याण्णव', 94: 'चौर्‍याण्णव', 95: 'पंच्याण्णव', 96: 'शहाण्णव', 97: 'सत्याण्णव', 98: 'अठ्ठ्याण्णव', 99: 'नव्याण्णव'
      };

      if (specialMarathi.containsKey(number)) {
        return specialMarathi[number]!;
      }
    }

    if (number < 20) {
      return ones[number];
    } else if (number < 100) {
      int tensDigit = number ~/ 10;
      int onesDigit = number % 10;
      if (onesDigit == 0) {
        return tens[tensDigit];
      } else {
        return tens[tensDigit] + ones[onesDigit];
      }
    }
    return number.toString();
  }
  static Future<String?> generateProfessionalWarganiReceipt(
      BuildContext context, Wargani wargani, String? userName, {String? forceLanguage}) async {
    final localizations = AppLocalizations.of(context)!;
    final profileBox = HiveHelper.getProfileBox();
    if (profileBox.isEmpty) {
      if (context.mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(localizations.profile),
            content: Text(localizations.profileSaved),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(localizations.ok),
              ),
            ],
          ),
        );
      }
      return null;
    }

    final profile = profileBox.getAt(0)!;
    final pdf = pw.Document();
    final font = await PdfGoogleFonts.notoSansDevanagariRegular();
    final boldFont = await PdfGoogleFonts.notoSansDevanagariMedium();

    pdf.addPage(
      pw.Page(
        pageFormat: const PdfPageFormat(
          21.0 * PdfPageFormat.cm, // Receipt width
          14.8 * PdfPageFormat.cm, // Receipt height (A5 landscape)
        ),
        margin: const pw.EdgeInsets.all(10),
        build: (pw.Context context) {
          return _buildProfessionalReceiptLayout(
            localizations,
            font,
            boldFont,
            profile,
            wargani,
            forceLanguage,
          );
        },
      ),
    );

    if (kIsWeb) {
      await Printing.layoutPdf(onLayout: (PdfPageFormat format) async => pdf.save());
      return null;
    } else {
      final output = await getTemporaryDirectory();
      final file = File("${output.path}/wargani_receipt_${wargani.receiptNo}.pdf");
      await file.writeAsBytes(await pdf.save());
      return file.path;
    }
  }

  static Future<String?> generate(
      BuildContext context, Wargani wargani, String? userName) async {
    final localizations = AppLocalizations.of(context)!;
    final profileBox = HiveHelper.getProfileBox();
    if (profileBox.isEmpty) {
      if (context.mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(localizations.profile),
            content:
                Text(localizations.profileSaved), // Placeholder for profile needed
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Text(localizations.ok),
              ),
            ],
          ),
        );
      }
      return null;
    }
    final profile = profileBox.getAt(0)!;
    final pdf = pw.Document();

    // Load Noto Sans Devanagari font for better Marathi/Hindi support
    final font = await PdfGoogleFonts.notoSansDevanagariRegular();

    final shivajiMaharajImage =
        await rootBundle.load("assets/images/shivaji_maharaj.png");
    final ambedkarImage = await rootBundle.load("assets/images/ambedkar.png");

    final shivajiMaharajImageProvider =
        pw.MemoryImage(shivajiMaharajImage.buffer.asUint8List());
    final ambedkarImageProvider =
        pw.MemoryImage(ambedkarImage.buffer.asUint8List());

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a5.landscape,
        build: (pw.Context context) {
          return pw.Row(
            children: [
              _buildReceiptSide(
                localizations,
                font,
                profile,
                wargani,
                shivajiMaharajImageProvider,
                ambedkarImageProvider,
                isCounterfoil: true,
              ),
              pw.VerticalDivider(width: 2, color: PdfColors.black),
              _buildReceiptSide(
                localizations,
                font,
                profile,
                wargani,
                shivajiMaharajImageProvider,
                ambedkarImageProvider,
                ganeshaImage: profile.logoBytes != null
                    ? pw.MemoryImage(profile.logoBytes!)
                    : null,
              ),
            ],
          );
        },
      ),
    );

    if (kIsWeb) {
      await Printing.layoutPdf(
          onLayout: (PdfPageFormat format) async => pdf.save());
      return null;
    } else {
      final output = await getTemporaryDirectory();
      final file =
          File("${output.path}/receipt_wargani_${wargani.receiptNo}.pdf");
      await file.writeAsBytes(await pdf.save());
      return file.path;
    }
  }

  static pw.Widget _buildProfessionalReceiptLayout(
    AppLocalizations localizations,
    pw.Font font,
    pw.Font boldFont,
    Profile profile,
    Wargani wargani,
    String? forceLanguage,
  ) {
    // Get current locale to determine language
    final currentLang = forceLanguage ?? HiveHelper.getLocale() ?? localizations.localeName;
    final isMarathi = currentLang == 'mr';
    final isHindi = currentLang == 'hi';

    // Debug print
    print('PDF Language Detection: forceLanguage=$forceLanguage, currentLang=$currentLang, localeName=${localizations.localeName}, isMarathi=$isMarathi, isHindi=$isHindi');

    // Language-specific text
    String getLocalizedText(String english, String marathi, String hindi) {
      if (isMarathi) return marathi;
      if (isHindi) return hindi;
      return english;
    }

    return pw.Container(
      decoration: pw.BoxDecoration(
        color: PdfColor.fromHex("#FFF8DC"), // Cornsilk background like in image
        border: pw.Border.all(color: PdfColors.orange, width: 2),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        children: [
          // Header with logos and title
          pw.Container(
            width: double.infinity,
            padding: const pw.EdgeInsets.all(10),
            decoration: pw.BoxDecoration(
              color: PdfColor.fromHex("#FF8C00"), // Orange header
              borderRadius: const pw.BorderRadius.only(
                topLeft: pw.Radius.circular(6),
                topRight: pw.Radius.circular(6),
              ),
            ),
            child: pw.Row(
              children: [
                // Left logo (if available)
                if (profile.logoBytes != null) ...[
                  pw.Container(
                    height: 50,
                    width: 50,
                    child: pw.Image(pw.MemoryImage(profile.logoBytes!)),
                  ),
                  pw.SizedBox(width: 10),
                ],

                // Center content
                pw.Expanded(
                  child: pw.Column(
                    children: [
                      // Title based on language
                      pw.Text(
                        isMarathi ? '|| श्री गणेश प्रसन्न ||' :
                        isHindi ? '|| श्री गणेश प्रसन्न ||' :
                        '|| Lord Ganesha is pleased ||',
                        style: pw.TextStyle(
                          font: boldFont,
                          fontSize: 14,
                          color: PdfColors.white,
                        ),
                        textAlign: pw.TextAlign.center,
                      ),
                      pw.SizedBox(height: 3),

                      // Registration number
                      if (profile.mandalRegistrationNo != null) ...[
                        pw.Text(
                          isMarathi ? 'रजि नं. ${profile.mandalRegistrationNo}' :
                          isHindi ? 'रजि नं. ${profile.mandalRegistrationNo}' :
                          'Reg. No. ${profile.mandalRegistrationNo}',
                          style: pw.TextStyle(
                            font: font,
                            fontSize: 10,
                            color: PdfColors.white,
                          ),
                        ),
                        pw.SizedBox(height: 2),
                      ],

                      // Mandal name
                      pw.Text(
                        profile.mandalName,
                        style: pw.TextStyle(
                          font: boldFont,
                          fontSize: 12,
                          color: PdfColors.white,
                        ),
                        textAlign: pw.TextAlign.center,
                      ),

                      // Address
                      pw.Text(
                        profile.address,
                        style: pw.TextStyle(
                          font: font,
                          fontSize: 10,
                          color: PdfColors.white,
                        ),
                        textAlign: pw.TextAlign.center,
                      ),

                      // Festival Year based on language
                      pw.Text(
                        isMarathi ? '${profile.festivalName ?? 'गणेशोत्सव'} ${profile.currentYear}' :
                        isHindi ? '${profile.festivalName ?? 'गणेशोत्सव'} ${profile.currentYear}' :
                        '${profile.festivalName ?? 'Ganesh Festival'} ${profile.currentYear}',
                        style: pw.TextStyle(
                          font: boldFont,
                          fontSize: 11,
                          color: PdfColors.white,
                        ),
                        textAlign: pw.TextAlign.center,
                      ),
                    ],
                  ),
                ),

                // Right logo space (for future use)
                pw.SizedBox(width: 60), // Placeholder for right logo
              ],
            ),
          ),

          // Main content
          pw.Expanded(
            child: pw.Padding(
              padding: const pw.EdgeInsets.all(20),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  // Receipt details
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Text(
                        isMarathi ? 'पावती नं. ${wargani.receiptNo}' :
                        isHindi ? 'रसीद क्रमांक ${wargani.receiptNo}' :
                        'Receipt No. ${wargani.receiptNo}',
                        style: pw.TextStyle(font: boldFont, fontSize: 14),
                      ),
                      pw.Text(
                        isMarathi ? 'दिनांक: ${DateFormat('dd/MM/yyyy').format(wargani.date)}' :
                        isHindi ? 'दिनांक: ${DateFormat('dd/MM/yyyy').format(wargani.date)}' :
                        'Date: ${DateFormat('dd/MM/yyyy').format(wargani.date)}',
                        style: pw.TextStyle(font: font, fontSize: 12),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 20),

                  // Name section
                  pw.Row(
                    children: [
                      pw.Text(
                        isMarathi ? 'सौ.श्री ' :
                        isHindi ? 'श्रीमती/श्री ' :
                        'Mrs./Mr. ',
                        style: pw.TextStyle(font: font, fontSize: 14),
                      ),
                      pw.Expanded(
                        child: pw.Container(
                          decoration: const pw.BoxDecoration(
                            border: pw.Border(
                              bottom: pw.BorderSide(color: PdfColors.black),
                            ),
                          ),
                          child: pw.Text(
                            wargani.name,
                            style: pw.TextStyle(font: boldFont, fontSize: 14),
                          ),
                        ),
                      ),
                      pw.Text(
                        isMarathi ? ' यांच्याकडून' :
                        isHindi ? ' इनके द्वारा' :
                        ' From',
                        style: pw.TextStyle(font: font, fontSize: 14),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 20),

                  // Amount section
                  pw.Row(
                    children: [
                      pw.Text(
                        isMarathi ? 'रोख रक्कम प्राप्त झाली: ' :
                        isHindi ? 'नकद राशि प्राप्त हुई: ' :
                        'Cash Amount Received: ',
                        style: pw.TextStyle(font: font, fontSize: 14),
                      ),
                      pw.Container(
                        padding: const pw.EdgeInsets.symmetric(horizontal: 15, vertical: 5),
                        decoration: pw.BoxDecoration(
                          border: pw.Border.all(color: PdfColors.black, width: 2),
                          borderRadius: pw.BorderRadius.circular(5),
                        ),
                        child: pw.Text(
                          '₹ ${wargani.amount}',
                          style: pw.TextStyle(font: boldFont, fontSize: 16),
                        ),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 15),

                  // Amount in words - Auto generate based on language
                  pw.Row(
                    children: [
                      pw.Text(
                        isMarathi ? 'अक्षरी रक्कम: ' :
                        isHindi ? 'शब्दों में राशि: ' :
                        'Amount in Words: ',
                        style: pw.TextStyle(font: font, fontSize: 12),
                      ),
                      pw.Expanded(
                        child: pw.Container(
                          decoration: const pw.BoxDecoration(
                            border: pw.Border(
                              bottom: pw.BorderSide(color: PdfColors.black),
                            ),
                          ),
                          child: pw.Text(
                            _getAmountInWords(wargani.amount.toDouble(), isMarathi, isHindi),
                            style: pw.TextStyle(font: font, fontSize: 12),
                          ),
                        ),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 20),

                  pw.Spacer(),

                  // Footer
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Text(
                            isMarathi ? 'धन्यवाद...!' :
                            isHindi ? 'धन्यवाद...!' :
                            'Thank You...!',
                            style: pw.TextStyle(font: boldFont, fontSize: 14),
                          ),
                          pw.SizedBox(height: 10),
                          pw.Text(
                            isMarathi ? 'सही' :
                            isHindi ? 'हस्ताक्षर' :
                            'Signature',
                            style: pw.TextStyle(font: font, fontSize: 12),
                          ),
                        ],
                      ),
                      pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.end,
                        children: [
                          pw.Text(
                            isMarathi ? 'रोख मिळाले...!' :
                            isHindi ? 'नकद प्राप्त हुआ...!' :
                            'Cash Received...!',
                            style: pw.TextStyle(font: boldFont, fontSize: 14),
                          ),
                        ],
                      ),
                    ],
                  ),

                  // AMSSoftX Credit
                  pw.SizedBox(height: 10),
                  pw.Text(
                    'Developed by AMSSoftX Web: https://amssoftx.com',
                    style: pw.TextStyle(
                      font: font,
                      fontSize: 6,
                      color: PdfColors.grey600,
                    ),
                    textAlign: pw.TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildReceiptSide(
    AppLocalizations localizations,
    pw.Font font,
    Profile profile,
    Wargani wargani,
    pw.ImageProvider shivajiMaharajImageProvider,
    pw.ImageProvider ambedkarImageProvider, {
    bool isCounterfoil = false,
    pw.ImageProvider? ganeshaImage,
  }) {
    return pw.Expanded(
      child: pw.Container(
        padding: const pw.EdgeInsets.all(10),
        decoration: pw.BoxDecoration(
          color: PdfColor.fromHex("#FFD700"), // Gold color
          border: pw.Border.all(color: PdfColors.black, width: 1),
        ),
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.center,
          children: [
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Text(
                  localizations.shreeGaneshPrasanna,
                  style: pw.TextStyle(font: font, fontSize: 10),
                ),
                pw.Row(
                  children: [
                    pw.Image(shivajiMaharajImageProvider, height: 25),
                    pw.SizedBox(width: 5),
                    pw.Image(ambedkarImageProvider, height: 25),
                  ],
                )
              ],
            ),
            pw.SizedBox(height: 5),
            pw.Text(
              profile.mandalName,
              style: pw.TextStyle(
                  font: font, fontSize: 14, fontWeight: pw.FontWeight.bold),
            ),
            pw.Text(
              localizations.ganeshotsavYear(profile.currentYear),
              style: pw.TextStyle(font: font, fontSize: 12),
            ),
            pw.SizedBox(height: 10),
            if (ganeshaImage != null)
              pw.Container(
                height: 80,
                child: pw.Image(ganeshaImage),
              ),
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Text('${localizations.receiptNo}: ${wargani.receiptNo}',
                    style: pw.TextStyle(font: font)),
                pw.Text(
                    '${localizations.date}: ${DateFormat('dd/MM/yyyy').format(wargani.date)}',
                    style: pw.TextStyle(font: font)),
              ],
            ),
            pw.SizedBox(height: 5),
            pw.Row(
              children: [
                pw.Text('${localizations.prefix} ${wargani.name}',
                    style: pw.TextStyle(font: font)),
                pw.Expanded(
                    child: pw.Divider(
                        color: PdfColors.black,
                        )),
                pw.Text(localizations.from, style: pw.TextStyle(font: font)),
              ],
            ),
            pw.SizedBox(height: 5),
            pw.Row(
              children: [
                pw.Text('${localizations.amountInWords}: ${wargani.amountInWords}',
                    style: pw.TextStyle(font: font)),
                pw.Expanded(
                    child: pw.Divider(
                        color: PdfColors.black,
                        )),
              ],
            ),
            pw.SizedBox(height: 10),
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Text(localizations.cashReceived,
                    style: pw.TextStyle(font: font)),
                pw.Container(
                  padding:
                      const pw.EdgeInsets.symmetric(horizontal: 10, vertical: 2),
                  decoration: pw.BoxDecoration(
                    border: pw.Border.all(color: PdfColors.black),
                    borderRadius: pw.BorderRadius.circular(5),
                  ),
                  child: pw.Text('₹ ${wargani.amount.toStringAsFixed(2)}',
                      style: pw.TextStyle(
                          font: font,
                          fontSize: 14,
                          fontWeight: pw.FontWeight.bold)),
                ),
              ],
            ),
            pw.Spacer(),
            pw.Align(
              alignment: pw.Alignment.bottomRight,
              child: pw.Text('${localizations.signature}: ....................',
                  style: pw.TextStyle(font: font)),
            ),
            if (!isCounterfoil)
              pw.Center(
                child: pw.Text(
                  localizations.thankYou,
                  style: pw.TextStyle(
                      font: font, fontStyle: pw.FontStyle.italic),
                ),
              )
          ],
        ),
      ),
    );
  }

  static Future<String?> generateDonationReceipt(
      BuildContext context, Donation donation, String? userName) async {
    final localizations = AppLocalizations.of(context)!;
    final profileBox = HiveHelper.getProfileBox();
    if (profileBox.isEmpty) {
      if (context.mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(localizations.profile),
            content: Text(localizations.profileSaved),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(localizations.ok),
              ),
            ],
          ),
        );
      }
      return null;
    }

    final profile = profileBox.getAt(0)!;
    final pdf = pw.Document();
    final font = await PdfGoogleFonts.notoSansDevanagariRegular();
    final boldFont = await PdfGoogleFonts.notoSansDevanagariMedium();

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(20),
        build: (pw.Context context) {
          return _buildProfessionalDonationLayout(
            localizations,
            font,
            boldFont,
            profile,
            donation,
          );
        },
      ),
    );

    if (kIsWeb) {
      await Printing.layoutPdf(onLayout: (PdfPageFormat format) async => pdf.save());
      return null;
    } else {
      final output = await getTemporaryDirectory();
      final file = File("${output.path}/donation_receipt_${donation.key}.pdf");
      await file.writeAsBytes(await pdf.save());
      return file.path;
    }
  }

  static pw.Widget _buildProfessionalDonationLayout(
    AppLocalizations localizations,
    pw.Font font,
    pw.Font boldFont,
    Profile profile,
    Donation donation,
  ) {
    final isMarathi = localizations.localeName == 'mr';
    final isHindi = localizations.localeName == 'hi';

    return pw.Container(
      decoration: pw.BoxDecoration(
        color: PdfColor.fromHex("#F0FFF0"), // Light green background
        border: pw.Border.all(color: PdfColors.green, width: 3),
        borderRadius: pw.BorderRadius.circular(15),
      ),
      child: pw.Column(
        children: [
          // Header
          pw.Container(
            width: double.infinity,
            padding: const pw.EdgeInsets.all(15),
            decoration: pw.BoxDecoration(
              color: PdfColors.green,
              borderRadius: const pw.BorderRadius.only(
                topLeft: pw.Radius.circular(12),
                topRight: pw.Radius.circular(12),
              ),
            ),
            child: pw.Column(
              children: [
                if (profile.logoBytes != null) ...[
                  pw.Container(
                    height: 60,
                    width: 60,
                    child: pw.Image(pw.MemoryImage(profile.logoBytes!)),
                  ),
                  pw.SizedBox(height: 8),
                ],
                pw.Text(
                  isMarathi ? '|| दान पावती ||' :
                  isHindi ? '|| दान रसीद ||' :
                  '|| Donation Receipt ||',
                  style: pw.TextStyle(
                    font: boldFont,
                    fontSize: 18,
                    color: PdfColors.white,
                  ),
                  textAlign: pw.TextAlign.center,
                ),
                pw.SizedBox(height: 5),
                pw.Text(
                  profile.mandalName,
                  style: pw.TextStyle(
                    font: boldFont,
                    fontSize: 16,
                    color: PdfColors.white,
                  ),
                  textAlign: pw.TextAlign.center,
                ),
                pw.Text(
                  profile.address,
                  style: pw.TextStyle(
                    font: font,
                    fontSize: 12,
                    color: PdfColors.white,
                  ),
                  textAlign: pw.TextAlign.center,
                ),
              ],
            ),
          ),

          // Main content
          pw.Expanded(
            child: pw.Padding(
              padding: const pw.EdgeInsets.all(20),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  // Date
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Text(
                        isMarathi ? 'दान पावती' :
                        isHindi ? 'दान रसीद' :
                        'Donation Receipt',
                        style: pw.TextStyle(font: boldFont, fontSize: 16),
                      ),
                      pw.Text(
                        isMarathi ? 'दिनांक: ${DateFormat('dd/MM/yyyy').format(donation.date)}' :
                        isHindi ? 'दिनांक: ${DateFormat('dd/MM/yyyy').format(donation.date)}' :
                        'Date: ${DateFormat('dd/MM/yyyy').format(donation.date)}',
                        style: pw.TextStyle(font: font, fontSize: 12),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 20),

                  // Donor name
                  pw.Row(
                    children: [
                      pw.Text(
                        isMarathi ? 'दानदाता: ' :
                        isHindi ? 'दानदाता: ' :
                        'Donor: ',
                        style: pw.TextStyle(font: font, fontSize: 14),
                      ),
                      pw.Expanded(
                        child: pw.Container(
                          decoration: const pw.BoxDecoration(
                            border: pw.Border(
                              bottom: pw.BorderSide(color: PdfColors.black),
                            ),
                          ),
                          child: pw.Text(
                            donation.donorName,
                            style: pw.TextStyle(font: boldFont, fontSize: 14),
                          ),
                        ),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 20),

                  // Amount
                  pw.Row(
                    children: [
                      pw.Text(
                        isMarathi ? 'दान रक्कम: ' :
                        isHindi ? 'दान राशि: ' :
                        'Donation Amount: ',
                        style: pw.TextStyle(font: font, fontSize: 14),
                      ),
                      pw.Container(
                        padding: const pw.EdgeInsets.symmetric(horizontal: 15, vertical: 5),
                        decoration: pw.BoxDecoration(
                          border: pw.Border.all(color: PdfColors.green, width: 2),
                          borderRadius: pw.BorderRadius.circular(5),
                        ),
                        child: pw.Text(
                          '₹ ${donation.amount}',
                          style: pw.TextStyle(font: boldFont, fontSize: 16),
                        ),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 15),

                  // Reason if provided
                  if (donation.reason != null && donation.reason!.isNotEmpty) ...[
                    pw.Row(
                      children: [
                        pw.Text(
                          isMarathi ? 'कारण: ' :
                          isHindi ? 'कारण: ' :
                          'Purpose: ',
                          style: pw.TextStyle(font: font, fontSize: 12),
                        ),
                        pw.Expanded(
                          child: pw.Container(
                            decoration: const pw.BoxDecoration(
                              border: pw.Border(
                                bottom: pw.BorderSide(color: PdfColors.black),
                              ),
                            ),
                            child: pw.Text(
                              donation.reason!,
                              style: pw.TextStyle(font: font, fontSize: 12),
                            ),
                          ),
                        ),
                      ],
                    ),
                    pw.SizedBox(height: 20),
                  ],

                  pw.Spacer(),

                  // Footer
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Text(
                            isMarathi ? 'आपल्या दानाबद्दल धन्यवाद!' :
                            isHindi ? 'आपके दान के लिए धन्यवाद!' :
                            'Thank you for your donation!',
                            style: pw.TextStyle(font: boldFont, fontSize: 14),
                          ),
                          pw.SizedBox(height: 10),
                          pw.Text(
                            isMarathi ? 'सही' :
                            isHindi ? 'हस्ताक्षर' :
                            'Signature',
                            style: pw.TextStyle(font: font, fontSize: 12),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  static Future<String?> generateDonationReceiptOld(
      BuildContext context, Donation donation, String? userName) async {
    final localizations = AppLocalizations.of(context)!;
    final profileBox = HiveHelper.getProfileBox();
    if (profileBox.isEmpty) {
      if (context.mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(localizations.profile),
            content: Text(localizations.profileSaved),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(localizations.ok),
              ),
            ],
          ),
        );
      }
      return null;
    }
    final profile = profileBox.getAt(0)!;
    final pdf = pw.Document();

    final font = await PdfGoogleFonts.notoSansDevanagariRegular();

    final shivajiMaharajImage =
        await rootBundle.load("assets/images/shivaji_maharaj.png");
    final ambedkarImage = await rootBundle.load("assets/images/ambedkar.png");

    final shivajiMaharajImageProvider =
        pw.MemoryImage(shivajiMaharajImage.buffer.asUint8List());
    final ambedkarImageProvider =
        pw.MemoryImage(ambedkarImage.buffer.asUint8List());

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a5.landscape,
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.center,
            children: [
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text(
                    localizations.shreeGaneshPrasanna,
                    style: pw.TextStyle(font: font, fontSize: 10),
                  ),
                  pw.Row(
                    children: [
                      pw.Image(shivajiMaharajImageProvider, height: 25),
                      pw.SizedBox(width: 5),
                      pw.Image(ambedkarImageProvider, height: 25),
                    ],
                  )
                ],
              ),
              pw.SizedBox(height: 5),
              pw.Text(
                profile.mandalName,
                style: pw.TextStyle(
                    font: font, fontSize: 14, fontWeight: pw.FontWeight.bold),
              ),
              pw.Text(
                localizations.ganeshotsavYear(profile.currentYear),
                style: pw.TextStyle(font: font, fontSize: 12),
              ),
              pw.SizedBox(height: 10),
              pw.Text(
                '${localizations.receiptNo}: ${donation.key}', // Assuming key as receipt no
                style: pw.TextStyle(font: font),
              ),
              pw.Text(
                '${localizations.date}: ${DateFormat('dd/MM/yyyy').format(donation.date)}',
                style: pw.TextStyle(font: font),
              ),
              pw.SizedBox(height: 5),
              pw.Text(
                '${localizations.donorName}: ${donation.donorName}',
                style: pw.TextStyle(font: font),
              ),
              pw.Text(
                '${localizations.amount}: ₹${donation.amount.toStringAsFixed(2)}',
                style: pw.TextStyle(font: font),
              ),
              if (donation.reason != null && donation.reason!.isNotEmpty)
                pw.Text(
                  '${localizations.reason}: ${donation.reason}',
                  style: pw.TextStyle(font: font),
                ),
              pw.Spacer(),
              pw.Align(
                alignment: pw.Alignment.bottomRight,
                child: pw.Text('${localizations.signature}: ....................',
                    style: pw.TextStyle(font: font)),
              ),
              pw.Center(
                child: pw.Text(
                  localizations.thankYou,
                  style: pw.TextStyle(font: font, fontStyle: pw.FontStyle.italic),
                ),
              )
            ],
          );
        },
      ),
    );

    if (kIsWeb) {
      await Printing.layoutPdf(
          onLayout: (PdfPageFormat format) async => pdf.save());
      return null;
    } else {
      final output = await getTemporaryDirectory();
      final file =
          File("${output.path}/receipt_donation_${donation.key}.pdf");
      await file.writeAsBytes(await pdf.save());
      return file.path;
    }
  }

  static Future<String?> generateExpenseReport(
      BuildContext context, Expense expense, String? userName) async {
    final localizations = AppLocalizations.of(context)!;
    final profileBox = HiveHelper.getProfileBox();
    if (profileBox.isEmpty) {
      if (context.mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(localizations.profile),
            content: Text(localizations.profileSaved),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(localizations.ok),
              ),
            ],
          ),
        );
      }
      return null;
    }

    final profile = profileBox.getAt(0)!;
    final pdf = pw.Document();
    final font = await PdfGoogleFonts.notoSansDevanagariRegular();
    final boldFont = await PdfGoogleFonts.notoSansDevanagariMedium();

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(20),
        build: (pw.Context context) {
          return _buildProfessionalExpenseLayout(
            localizations,
            font,
            boldFont,
            profile,
            expense,
          );
        },
      ),
    );

    if (kIsWeb) {
      await Printing.layoutPdf(onLayout: (PdfPageFormat format) async => pdf.save());
      return null;
    } else {
      final output = await getTemporaryDirectory();
      final file = File("${output.path}/expense_report_${expense.key}.pdf");
      await file.writeAsBytes(await pdf.save());
      return file.path;
    }
  }

  static pw.Widget _buildProfessionalExpenseLayout(
    AppLocalizations localizations,
    pw.Font font,
    pw.Font boldFont,
    Profile profile,
    Expense expense,
  ) {
    final isMarathi = localizations.localeName == 'mr';
    final isHindi = localizations.localeName == 'hi';

    return pw.Container(
      decoration: pw.BoxDecoration(
        color: PdfColor.fromHex("#FFF5F5"), // Light red background
        border: pw.Border.all(color: PdfColors.red, width: 3),
        borderRadius: pw.BorderRadius.circular(15),
      ),
      child: pw.Column(
        children: [
          // Header
          pw.Container(
            width: double.infinity,
            padding: const pw.EdgeInsets.all(15),
            decoration: pw.BoxDecoration(
              color: PdfColors.red,
              borderRadius: const pw.BorderRadius.only(
                topLeft: pw.Radius.circular(12),
                topRight: pw.Radius.circular(12),
              ),
            ),
            child: pw.Column(
              children: [
                if (profile.logoBytes != null) ...[
                  pw.Container(
                    height: 60,
                    width: 60,
                    child: pw.Image(pw.MemoryImage(profile.logoBytes!)),
                  ),
                  pw.SizedBox(height: 8),
                ],
                pw.Text(
                  isMarathi ? '|| खर्च अहवाल ||' :
                  isHindi ? '|| व्यय रिपोर्ट ||' :
                  '|| Expense Report ||',
                  style: pw.TextStyle(
                    font: boldFont,
                    fontSize: 18,
                    color: PdfColors.white,
                  ),
                  textAlign: pw.TextAlign.center,
                ),
                pw.SizedBox(height: 5),
                pw.Text(
                  profile.mandalName,
                  style: pw.TextStyle(
                    font: boldFont,
                    fontSize: 16,
                    color: PdfColors.white,
                  ),
                  textAlign: pw.TextAlign.center,
                ),
                pw.Text(
                  profile.address,
                  style: pw.TextStyle(
                    font: font,
                    fontSize: 12,
                    color: PdfColors.white,
                  ),
                  textAlign: pw.TextAlign.center,
                ),
              ],
            ),
          ),

          // Main content
          pw.Expanded(
            child: pw.Padding(
              padding: const pw.EdgeInsets.all(20),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  // Date
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Text(
                        isMarathi ? 'खर्च अहवाल' :
                        isHindi ? 'व्यय रिपोर्ट' :
                        'Expense Report',
                        style: pw.TextStyle(font: boldFont, fontSize: 16),
                      ),
                      pw.Text(
                        isMarathi ? 'दिनांक: ${DateFormat('dd/MM/yyyy').format(expense.date)}' :
                        isHindi ? 'दिनांक: ${DateFormat('dd/MM/yyyy').format(expense.date)}' :
                        'Date: ${DateFormat('dd/MM/yyyy').format(expense.date)}',
                        style: pw.TextStyle(font: font, fontSize: 12),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 20),

                  // Expense title
                  pw.Row(
                    children: [
                      pw.Text(
                        isMarathi ? 'खर्चाचे नाव: ' :
                        isHindi ? 'व्यय का नाम: ' :
                        'Expense Title: ',
                        style: pw.TextStyle(font: font, fontSize: 14),
                      ),
                      pw.Expanded(
                        child: pw.Container(
                          decoration: const pw.BoxDecoration(
                            border: pw.Border(
                              bottom: pw.BorderSide(color: PdfColors.black),
                            ),
                          ),
                          child: pw.Text(
                            expense.title,
                            style: pw.TextStyle(font: boldFont, fontSize: 14),
                          ),
                        ),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 20),

                  // Amount
                  pw.Row(
                    children: [
                      pw.Text(
                        isMarathi ? 'खर्च रक्कम: ' :
                        isHindi ? 'व्यय राशि: ' :
                        'Expense Amount: ',
                        style: pw.TextStyle(font: font, fontSize: 14),
                      ),
                      pw.Container(
                        padding: const pw.EdgeInsets.symmetric(horizontal: 15, vertical: 5),
                        decoration: pw.BoxDecoration(
                          border: pw.Border.all(color: PdfColors.red, width: 2),
                          borderRadius: pw.BorderRadius.circular(5),
                        ),
                        child: pw.Text(
                          '₹ ${expense.amount}',
                          style: pw.TextStyle(font: boldFont, fontSize: 16),
                        ),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 15),

                  // Description
                  if (expense.description.isNotEmpty) ...[
                    pw.Row(
                      children: [
                        pw.Text(
                          isMarathi ? 'तपशील: ' :
                          isHindi ? 'विवरण: ' :
                          'Description: ',
                          style: pw.TextStyle(font: font, fontSize: 12),
                        ),
                        pw.Expanded(
                          child: pw.Container(
                            decoration: const pw.BoxDecoration(
                              border: pw.Border(
                                bottom: pw.BorderSide(color: PdfColors.black),
                              ),
                            ),
                            child: pw.Text(
                              expense.description,
                              style: pw.TextStyle(font: font, fontSize: 12),
                            ),
                          ),
                        ),
                      ],
                    ),
                    pw.SizedBox(height: 20),
                  ],

                  pw.Spacer(),

                  // Footer
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Text(
                            isMarathi ? 'मंजूर केले' :
                            isHindi ? 'स्वीकृत' :
                            'Approved',
                            style: pw.TextStyle(font: boldFont, fontSize: 14),
                          ),
                          pw.SizedBox(height: 10),
                          pw.Text(
                            isMarathi ? 'सही' :
                            isHindi ? 'हस्ताक्षर' :
                            'Signature',
                            style: pw.TextStyle(font: font, fontSize: 12),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  static Future<String?> generateExpenseReportOld(
      BuildContext context, Expense expense, String? userName) async {
    final localizations = AppLocalizations.of(context)!;
    final profileBox = HiveHelper.getProfileBox();
    if (profileBox.isEmpty) {
      if (context.mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(localizations.profile),
            content: Text(localizations.profileSaved),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(localizations.ok),
              ),
            ],
          ),
        );
      }
      return null;
    }
    final profile = profileBox.getAt(0)!;
    final pdf = pw.Document();

    final font = await PdfGoogleFonts.notoSansDevanagariRegular();

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Center(
                child: pw.Text(
                  localizations.expensesSummary,
                  style: pw.TextStyle(font: font, fontSize: 20, fontWeight: pw.FontWeight.bold),
                ),
              ),
              pw.SizedBox(height: 20),
              pw.Text(
                '${localizations.title}: ${expense.title}',
                style: pw.TextStyle(font: font, fontSize: 14),
              ),
              pw.Text(
                '${localizations.amount}: ₹${expense.amount.toStringAsFixed(2)}',
                style: pw.TextStyle(font: font, fontSize: 14),
              ),
              pw.Text(
                '${localizations.description}: ${expense.description}',
                style: pw.TextStyle(font: font, fontSize: 14),
              ),
              pw.Text(
                '${localizations.date}: ${DateFormat('dd/MM/yyyy').format(expense.date)}',
                style: pw.TextStyle(font: font, fontSize: 14),
              ),
              pw.Spacer(),
              pw.Align(
                alignment: pw.Alignment.bottomRight,
                child: pw.Text('${localizations.generatedBy}: ${userName ?? 'Admin'}',
                    style: pw.TextStyle(font: font, fontSize: 10)),
              ),
            ],
          );
        },
      ),
    );

    if (kIsWeb) {
      await Printing.layoutPdf(
          onLayout: (PdfPageFormat format) async => pdf.save());
      return null;
    } else {
      final output = await getTemporaryDirectory();
      final file =
          File("${output.path}/expense_report_${expense.key}.pdf");
      await file.writeAsBytes(await pdf.save());
      return file.path;
    }
  }

  static Future<String?> generateAllExpensesReport(
      BuildContext context, List<Expense> expenses, String? userName) async {
    final localizations = AppLocalizations.of(context)!;
    final profileBox = HiveHelper.getProfileBox();
    if (profileBox.isEmpty) {
      if (context.mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(localizations.profile),
            content: Text(localizations.profileSaved),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Text(localizations.ok),
              ),
            ],
          ),
        );
      }
      return null;
    }
    final profile = profileBox.getAt(0)!;
    final pdf = pw.Document();

    final font = await PdfGoogleFonts.notoSansDevanagariRegular();

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return [
            pw.Center(
              child: pw.Text(
                localizations.expensesSummary,
                style: pw.TextStyle(font: font, fontSize: 24, fontWeight: pw.FontWeight.bold),
              ),
            ),
            pw.SizedBox(height: 20),
            pw.Table.fromTextArray(
              headers: [
                localizations.date,
                localizations.title,
                localizations.description,
                localizations.amount,
              ],
              data: expenses.map((e) => [
                DateFormat('dd/MM/yyyy').format(e.date),
                e.title,
                e.description,
                '₹${e.amount.toStringAsFixed(2)}',
              ]).toList(),
              headerStyle: pw.TextStyle(font: font, fontWeight: pw.FontWeight.bold),
              cellStyle: pw.TextStyle(font: font),
              border: pw.TableBorder.all(color: PdfColors.black),
              headerDecoration: const pw.BoxDecoration(color: PdfColors.grey300),
              columnWidths: {
                0: const pw.FlexColumnWidth(2),
                1: const pw.FlexColumnWidth(3),
                2: const pw.FlexColumnWidth(4),
                3: const pw.FlexColumnWidth(2),
              },
            ),
            pw.SizedBox(height: 20),
            pw.Align(
              alignment: pw.Alignment.bottomRight,
              child: pw.Text('${localizations.generatedBy}: ${userName ?? 'Admin'}',
                  style: pw.TextStyle(font: font, fontSize: 10)),
            ),
          ];
        },
      ),
    );

    if (kIsWeb) {
      await Printing.layoutPdf(
          onLayout: (PdfPageFormat format) async => pdf.save());
      return null;
    } else {
      final output = await getTemporaryDirectory();
      final file =
          File("${output.path}/all_expenses_report.pdf");
      await file.writeAsBytes(await pdf.save());
      return file.path;
    }
  }
}

class NumberToWords {
  static String convert(double number) {
    // This is a simplified version. A more robust solution would be needed for a real app.
    return number.toString();
  }
}
