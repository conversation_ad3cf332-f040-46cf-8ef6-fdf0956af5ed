import 'dart:io';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:wargani/models/profile_model.dart';
import 'package:wargani/models/wargani_model.dart';
import 'package:wargani/utils/hive_helper.dart';
import 'package:flutter/material.dart';
import 'package:wargani/models/donation_model.dart';
import 'package:wargani/models/expense_model.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wargani/l10n/app_localizations.dart';

class PdfGenerator {
  static Future<String?> generate(
      BuildContext context, Wargani wargani, String? userName) async {
    final localizations = AppLocalizations.of(context)!;
    final profileBox = HiveHelper.getProfileBox();
    if (profileBox.isEmpty) {
      if (context.mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(localizations.profile),
            content:
                Text(localizations.profileSaved), // Placeholder for profile needed
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Text(localizations.ok),
              ),
            ],
          ),
        );
      }
      return null;
    }
    final profile = profileBox.getAt(0)!;
    final pdf = pw.Document();

    // Load Noto Sans Devanagari font for better Marathi/Hindi support
    final font = await PdfGoogleFonts.notoSansDevanagariRegular();

    final shivajiMaharajImage =
        await rootBundle.load("assets/images/shivaji_maharaj.png");
    final ambedkarImage = await rootBundle.load("assets/images/ambedkar.png");

    final shivajiMaharajImageProvider =
        pw.MemoryImage(shivajiMaharajImage.buffer.asUint8List());
    final ambedkarImageProvider =
        pw.MemoryImage(ambedkarImage.buffer.asUint8List());

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a5.landscape,
        build: (pw.Context context) {
          return pw.Row(
            children: [
              _buildReceiptSide(
                localizations,
                font,
                profile,
                wargani,
                shivajiMaharajImageProvider,
                ambedkarImageProvider,
                isCounterfoil: true,
              ),
              pw.VerticalDivider(width: 2, color: PdfColors.black),
              _buildReceiptSide(
                localizations,
                font,
                profile,
                wargani,
                shivajiMaharajImageProvider,
                ambedkarImageProvider,
                ganeshaImage: profile.logoBytes != null
                    ? pw.MemoryImage(profile.logoBytes!)
                    : null,
              ),
            ],
          );
        },
      ),
    );

    if (kIsWeb) {
      await Printing.layoutPdf(
          onLayout: (PdfPageFormat format) async => pdf.save());
      return null;
    } else {
      final output = await getTemporaryDirectory();
      final file =
          File("${output.path}/receipt_wargani_${wargani.receiptNo}.pdf");
      await file.writeAsBytes(await pdf.save());
      return file.path;
    }
  }

  static pw.Widget _buildReceiptSide(
    AppLocalizations localizations,
    pw.Font font,
    Profile profile,
    Wargani wargani,
    pw.ImageProvider shivajiMaharajImageProvider,
    pw.ImageProvider ambedkarImageProvider, {
    bool isCounterfoil = false,
    pw.ImageProvider? ganeshaImage,
  }) {
    return pw.Expanded(
      child: pw.Container(
        padding: const pw.EdgeInsets.all(10),
        decoration: pw.BoxDecoration(
          color: PdfColor.fromHex("#FFD700"), // Gold color
          border: pw.Border.all(color: PdfColors.black, width: 1),
        ),
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.center,
          children: [
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Text(
                  localizations.shreeGaneshPrasanna,
                  style: pw.TextStyle(font: font, fontSize: 10),
                ),
                pw.Row(
                  children: [
                    pw.Image(shivajiMaharajImageProvider, height: 25),
                    pw.SizedBox(width: 5),
                    pw.Image(ambedkarImageProvider, height: 25),
                  ],
                )
              ],
            ),
            pw.SizedBox(height: 5),
            pw.Text(
              profile.mandalName,
              style: pw.TextStyle(
                  font: font, fontSize: 14, fontWeight: pw.FontWeight.bold),
            ),
            pw.Text(
              localizations.ganeshotsavYear(profile.currentYear),
              style: pw.TextStyle(font: font, fontSize: 12),
            ),
            pw.SizedBox(height: 10),
            if (ganeshaImage != null)
              pw.Container(
                height: 80,
                child: pw.Image(ganeshaImage),
              ),
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Text('${localizations.receiptNo}: ${wargani.receiptNo}',
                    style: pw.TextStyle(font: font)),
                pw.Text(
                    '${localizations.date}: ${DateFormat('dd/MM/yyyy').format(wargani.date)}',
                    style: pw.TextStyle(font: font)),
              ],
            ),
            pw.SizedBox(height: 5),
            pw.Row(
              children: [
                pw.Text('${localizations.prefix} ${wargani.name}',
                    style: pw.TextStyle(font: font)),
                pw.Expanded(
                    child: pw.Divider(
                        color: PdfColors.black,
                        )),
                pw.Text(localizations.from, style: pw.TextStyle(font: font)),
              ],
            ),
            pw.SizedBox(height: 5),
            pw.Row(
              children: [
                pw.Text('${localizations.amountInWords}: ${wargani.amountInWords}',
                    style: pw.TextStyle(font: font)),
                pw.Expanded(
                    child: pw.Divider(
                        color: PdfColors.black,
                        )),
              ],
            ),
            pw.SizedBox(height: 10),
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Text(localizations.cashReceived,
                    style: pw.TextStyle(font: font)),
                pw.Container(
                  padding:
                      const pw.EdgeInsets.symmetric(horizontal: 10, vertical: 2),
                  decoration: pw.BoxDecoration(
                    border: pw.Border.all(color: PdfColors.black),
                    borderRadius: pw.BorderRadius.circular(5),
                  ),
                  child: pw.Text('₹ ${wargani.amount.toStringAsFixed(2)}',
                      style: pw.TextStyle(
                          font: font,
                          fontSize: 14,
                          fontWeight: pw.FontWeight.bold)),
                ),
              ],
            ),
            pw.Spacer(),
            pw.Align(
              alignment: pw.Alignment.bottomRight,
              child: pw.Text('${localizations.signature}: ....................',
                  style: pw.TextStyle(font: font)),
            ),
            if (!isCounterfoil)
              pw.Center(
                child: pw.Text(
                  localizations.thankYou,
                  style: pw.TextStyle(
                      font: font, fontStyle: pw.FontStyle.italic),
                ),
              )
          ],
        ),
      ),
    );
  }

  static Future<String?> generateDonationReceipt(
      BuildContext context, Donation donation, String? userName) async {
    final localizations = AppLocalizations.of(context)!;
    final profileBox = HiveHelper.getProfileBox();
    if (profileBox.isEmpty) {
      if (context.mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(localizations.profile),
            content: Text(localizations.profileSaved),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Text(localizations.ok),
              ),
            ],
          ),
        );
      }
      return null;
    }
    final profile = profileBox.getAt(0)!;
    final pdf = pw.Document();

    final font = await PdfGoogleFonts.notoSansDevanagariRegular();

    final shivajiMaharajImage =
        await rootBundle.load("assets/images/shivaji_maharaj.png");
    final ambedkarImage = await rootBundle.load("assets/images/ambedkar.png");

    final shivajiMaharajImageProvider =
        pw.MemoryImage(shivajiMaharajImage.buffer.asUint8List());
    final ambedkarImageProvider =
        pw.MemoryImage(ambedkarImage.buffer.asUint8List());

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a5.landscape,
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.center,
            children: [
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text(
                    localizations.shreeGaneshPrasanna,
                    style: pw.TextStyle(font: font, fontSize: 10),
                  ),
                  pw.Row(
                    children: [
                      pw.Image(shivajiMaharajImageProvider, height: 25),
                      pw.SizedBox(width: 5),
                      pw.Image(ambedkarImageProvider, height: 25),
                    ],
                  )
                ],
              ),
              pw.SizedBox(height: 5),
              pw.Text(
                profile.mandalName,
                style: pw.TextStyle(
                    font: font, fontSize: 14, fontWeight: pw.FontWeight.bold),
              ),
              pw.Text(
                localizations.ganeshotsavYear(profile.currentYear),
                style: pw.TextStyle(font: font, fontSize: 12),
              ),
              pw.SizedBox(height: 10),
              pw.Text(
                '${localizations.receiptNo}: ${donation.key}', // Assuming key as receipt no
                style: pw.TextStyle(font: font),
              ),
              pw.Text(
                '${localizations.date}: ${DateFormat('dd/MM/yyyy').format(donation.date)}',
                style: pw.TextStyle(font: font),
              ),
              pw.SizedBox(height: 5),
              pw.Text(
                '${localizations.donorName}: ${donation.donorName}',
                style: pw.TextStyle(font: font),
              ),
              pw.Text(
                '${localizations.amount}: ₹${donation.amount.toStringAsFixed(2)}',
                style: pw.TextStyle(font: font),
              ),
              if (donation.reason != null && donation.reason!.isNotEmpty)
                pw.Text(
                  '${localizations.reason}: ${donation.reason}',
                  style: pw.TextStyle(font: font),
                ),
              pw.Spacer(),
              pw.Align(
                alignment: pw.Alignment.bottomRight,
                child: pw.Text('${localizations.signature}: ....................',
                    style: pw.TextStyle(font: font)),
              ),
              pw.Center(
                child: pw.Text(
                  localizations.thankYou,
                  style: pw.TextStyle(font: font, fontStyle: pw.FontStyle.italic),
                ),
              )
            ],
          );
        },
      ),
    );

    if (kIsWeb) {
      await Printing.layoutPdf(
          onLayout: (PdfPageFormat format) async => pdf.save());
      return null;
    } else {
      final output = await getTemporaryDirectory();
      final file =
          File("${output.path}/receipt_donation_${donation.key}.pdf");
      await file.writeAsBytes(await pdf.save());
      return file.path;
    }
  }

  static Future<String?> generateExpenseReport(
      BuildContext context, Expense expense, String? userName) async {
    final localizations = AppLocalizations.of(context)!;
    final profileBox = HiveHelper.getProfileBox();
    if (profileBox.isEmpty) {
      if (context.mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(localizations.profile),
            content: Text(localizations.profileSaved),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Text(localizations.ok),
              ),
            ],
          ),
        );
      }
      return null;
    }
    final profile = profileBox.getAt(0)!;
    final pdf = pw.Document();

    final font = await PdfGoogleFonts.notoSansDevanagariRegular();

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Center(
                child: pw.Text(
                  localizations.expensesSummary,
                  style: pw.TextStyle(font: font, fontSize: 20, fontWeight: pw.FontWeight.bold),
                ),
              ),
              pw.SizedBox(height: 20),
              pw.Text(
                '${localizations.title}: ${expense.title}',
                style: pw.TextStyle(font: font, fontSize: 14),
              ),
              pw.Text(
                '${localizations.amount}: ₹${expense.amount.toStringAsFixed(2)}',
                style: pw.TextStyle(font: font, fontSize: 14),
              ),
              pw.Text(
                '${localizations.description}: ${expense.description}',
                style: pw.TextStyle(font: font, fontSize: 14),
              ),
              pw.Text(
                '${localizations.date}: ${DateFormat('dd/MM/yyyy').format(expense.date)}',
                style: pw.TextStyle(font: font, fontSize: 14),
              ),
              pw.Spacer(),
              pw.Align(
                alignment: pw.Alignment.bottomRight,
                child: pw.Text('${localizations.generatedBy}: ${userName ?? 'Admin'}',
                    style: pw.TextStyle(font: font, fontSize: 10)),
              ),
            ],
          );
        },
      ),
    );

    if (kIsWeb) {
      await Printing.layoutPdf(
          onLayout: (PdfPageFormat format) async => pdf.save());
      return null;
    } else {
      final output = await getTemporaryDirectory();
      final file =
          File("${output.path}/expense_report_${expense.key}.pdf");
      await file.writeAsBytes(await pdf.save());
      return file.path;
    }
  }

  static Future<String?> generateAllExpensesReport(
      BuildContext context, List<Expense> expenses, String? userName) async {
    final localizations = AppLocalizations.of(context)!;
    final profileBox = HiveHelper.getProfileBox();
    if (profileBox.isEmpty) {
      if (context.mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(localizations.profile),
            content: Text(localizations.profileSaved),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Text(localizations.ok),
              ),
            ],
          ),
        );
      }
      return null;
    }
    final profile = profileBox.getAt(0)!;
    final pdf = pw.Document();

    final font = await PdfGoogleFonts.notoSansDevanagariRegular();

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return [
            pw.Center(
              child: pw.Text(
                localizations.expensesSummary,
                style: pw.TextStyle(font: font, fontSize: 24, fontWeight: pw.FontWeight.bold),
              ),
            ),
            pw.SizedBox(height: 20),
            pw.Table.fromTextArray(
              headers: [
                localizations.date,
                localizations.title,
                localizations.description,
                localizations.amount,
              ],
              data: expenses.map((e) => [
                DateFormat('dd/MM/yyyy').format(e.date),
                e.title,
                e.description,
                '₹${e.amount.toStringAsFixed(2)}',
              ]).toList(),
              headerStyle: pw.TextStyle(font: font, fontWeight: pw.FontWeight.bold),
              cellStyle: pw.TextStyle(font: font),
              border: pw.TableBorder.all(color: PdfColors.black),
              headerDecoration: const pw.BoxDecoration(color: PdfColors.grey300),
              columnWidths: {
                0: const pw.FlexColumnWidth(2),
                1: const pw.FlexColumnWidth(3),
                2: const pw.FlexColumnWidth(4),
                3: const pw.FlexColumnWidth(2),
              },
            ),
            pw.SizedBox(height: 20),
            pw.Align(
              alignment: pw.Alignment.bottomRight,
              child: pw.Text('${localizations.generatedBy}: ${userName ?? 'Admin'}',
                  style: pw.TextStyle(font: font, fontSize: 10)),
            ),
          ];
        },
      ),
    );

    if (kIsWeb) {
      await Printing.layoutPdf(
          onLayout: (PdfPageFormat format) async => pdf.save());
      return null;
    } else {
      final output = await getTemporaryDirectory();
      final file =
          File("${output.path}/all_expenses_report.pdf");
      await file.writeAsBytes(await pdf.save());
      return file.path;
    }
  }
}

class NumberToWords {
  static String convert(double number) {
    // This is a simplified version. A more robust solution would be needed for a real app.
    return number.toString();
  }
}
