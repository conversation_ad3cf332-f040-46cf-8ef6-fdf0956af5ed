import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:wargani/l10n/app_localizations.dart';
import 'package:wargani/models/profile_model.dart';
import 'package:wargani/models/user_model.dart'; // Import UserModel
import 'package:wargani/utils/hive_helper.dart';
import 'package:wargani/widgets/footer.dart';
import 'package:wargani/main.dart'; // Import WarganiApp
import 'package:wargani/screens/login_screen.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  _ProfileScreenState createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  final _mandalNameController = TextEditingController();
  final _addressController = TextEditingController();
  final _currentYearController = TextEditingController();
  final _mandalRegistrationNoController = TextEditingController();
  Uint8List? _logoBytes;
  User? _currentUser;
  bool _isEditing = false; // Add isEditing state

  @override
  void initState() {
    super.initState();
    _loadProfile();
    _loadCurrentUser();
  }

  void _loadCurrentUser() {
    // Assuming HiveHelper has a method to get the current logged-in user
    // For now, I'll just get the first user in the box as a placeholder.
    // In a real app, you'd have a proper login state management.
    final userBox = HiveHelper.getUsersBox();
    if (userBox.isNotEmpty) {
      setState(() {
        _currentUser = userBox.getAt(0); // Get the first user as current user
      });
    }
  }

  void _loadProfile() {
    final profileBox = HiveHelper.getProfileBox();
    if (profileBox.isNotEmpty) {
      final profile = profileBox.getAt(0)!;
      _mandalNameController.text = profile.mandalName;
      _addressController.text = profile.address;
      _currentYearController.text = profile.currentYear;
      _mandalRegistrationNoController.text = profile.mandalRegistrationNo ?? '';
      if (profile.logoBytes != null) {
        setState(() {
          _logoBytes = profile.logoBytes;
        });
      }
    }
  }

  Future<void> _pickLogo() async {
    final pickedFile =
        await ImagePicker().pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      final bytes = await pickedFile.readAsBytes();
      setState(() {
        _logoBytes = bytes;
      });
    }
  }

  void _saveProfile() {
    if (_formKey.currentState!.validate()) {
      final localizations = AppLocalizations.of(context)!;
      final profile = Profile(
        mandalName: _mandalNameController.text,
        address: _addressController.text,
        currentYear: _currentYearController.text,
        logoBytes: _logoBytes,
        mandalRegistrationNo: _mandalRegistrationNoController.text,
      );
      final profileBox = HiveHelper.getProfileBox();
      if (profileBox.isNotEmpty) {
        profileBox.putAt(0, profile);
      } else {
        profileBox.add(profile);
      }
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(localizations.profileSaved)),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.profile),
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Display User Profile Information
                    if (_currentUser != null) ...[
                      TextFormField(
                        initialValue: _currentUser!.name,
                        decoration: InputDecoration(labelText: localizations.userName),
                        readOnly: true,
                      ),
                      const SizedBox(height: 10),
                      TextFormField(
                        initialValue: _currentUser!.email,
                        decoration: InputDecoration(labelText: localizations.userEmail),
                        readOnly: true,
                      ),
                      const SizedBox(height: 20),
                    ],
                    TextFormField(
                      controller: _mandalNameController,
                      decoration:
                          InputDecoration(labelText: localizations.mandalName),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return localizations.pleaseEnterMandalName;
                        }
                        return null;
                      },
                      readOnly: !_isEditing,
                    ),
                    const SizedBox(height: 10),
                    TextFormField(
                      controller: _mandalRegistrationNoController,
                      decoration: InputDecoration(
                          labelText: localizations.registrationNo),
                      readOnly: !_isEditing,
                    ),
                    const SizedBox(height: 10),
                    TextFormField(
                      controller: _addressController,
                      decoration:
                          InputDecoration(labelText: localizations.address),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return localizations.pleaseEnterAddress;
                        }
                        return null;
                      },
                      readOnly: !_isEditing,
                    ),
                    const SizedBox(height: 10),
                    TextFormField(
                      controller: _currentYearController,
                      decoration:
                          InputDecoration(labelText: localizations.currentYear),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return localizations.pleaseEnterCurrentYear;
                        }
                        return null;
                      },
                      readOnly: !_isEditing,
                    ),
                    const SizedBox(height: 20),
                    Row(
                      children: [
                        _logoBytes == null
                            ? const Text('No logo selected.')
                            : Image.memory(_logoBytes!, height: 100),
                        const SizedBox(width: 20),
                        ElevatedButton(
                          onPressed: _isEditing ? _pickLogo : null,
                          child: Text(localizations.logo),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    if (_isEditing)
                      ElevatedButton(
                        onPressed: () {
                          _saveProfile();
                          setState(() {
                            _isEditing = false;
                          });
                        },
                        child: Text(localizations.save),
                      ),
                    const SizedBox(height: 20),
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          _isEditing = true;
                        });
                      },
                      child: const Text('Edit'),
                    ),
                    const SizedBox(height: 20),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pushReplacement(
                          MaterialPageRoute(
                            builder: (context) => const LoginScreen(),
                          ),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                      ),
                      child: Text(localizations.logout),
                    ),
                    const SizedBox(height: 20),
                    DropdownButtonFormField<String>(
                      decoration: InputDecoration(labelText: localizations.selectLanguage),
                      value: Localizations.localeOf(context).languageCode,
                      items: const [
                        DropdownMenuItem(
                          value: 'en',
                          child: Text('English'),
                        ),
                        DropdownMenuItem(
                          value: 'mr',
                          child: Text('मराठी'),
                        ),
                      ],
                      onChanged: (String? newValue) {
                        if (newValue != null) {
                          // TODO: Implement locale change functionality
                          HiveHelper.saveLocale(newValue);
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('Language changed. Please restart the app.')),
                          );
                        }
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
          const Column(
            children: [
              Text(
                'Developed by AMSSoftX Web : https://amssoftx.com',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 12),
              ),
              Text(
                'V1.1.0',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 12),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
