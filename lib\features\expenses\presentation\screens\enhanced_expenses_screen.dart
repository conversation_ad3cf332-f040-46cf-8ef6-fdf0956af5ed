import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:wargani/core/constants/app_constants.dart';
import 'package:wargani/core/utils/extensions.dart';
import 'package:wargani/core/utils/validators.dart';
import 'package:wargani/l10n/app_localizations.dart';
import 'package:wargani/models/expense_model.dart';
import 'package:wargani/shared/widgets/custom_button.dart';
import 'package:wargani/shared/widgets/custom_card.dart';
import 'package:wargani/shared/widgets/custom_text_field.dart';
import 'package:wargani/shared/widgets/loading_overlay.dart';
import 'package:wargani/utils/hive_helper.dart';
import 'package:hive_flutter/hive_flutter.dart';

/// Enhanced Expenses Screen with professional UI
class EnhancedExpensesScreen extends StatefulWidget {
  const EnhancedExpensesScreen({super.key});

  @override
  State<EnhancedExpensesScreen> createState() => _EnhancedExpensesScreenState();
}

class _EnhancedExpensesScreenState extends State<EnhancedExpensesScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  
  // Controllers
  final _titleController = TextEditingController();
  final _amountController = TextEditingController();
  final _descriptionController = TextEditingController();

  // Focus Nodes
  final _titleFocus = FocusNode();
  final _amountFocus = FocusNode();
  final _descriptionFocus = FocusNode();

  DateTime _selectedDate = DateTime.now();
  bool _isLoading = false;
  bool _showForm = false;

  late AnimationController _animationController;
  late AnimationController _fabAnimationController;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: AppConstants.mediumAnimation,
      vsync: this,
    );
    _fabAnimationController = AnimationController(
      duration: AppConstants.shortAnimation,
      vsync: this,
    );
    _animationController.forward();
    _fabAnimationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _fabAnimationController.dispose();
    _titleController.dispose();
    _amountController.dispose();
    _descriptionController.dispose();
    _titleFocus.dispose();
    _amountFocus.dispose();
    _descriptionFocus.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    
    return LoadingOverlay(
      isLoading: _isLoading,
      message: 'Processing expense...',
      child: Scaffold(
        backgroundColor: context.colorScheme.background,
        appBar: _buildAppBar(localizations),
        body: Column(
          children: [
            if (_showForm) _buildExpenseForm(localizations),
            Expanded(child: _buildExpensesList(localizations)),
          ],
        ),
        floatingActionButton: _buildFloatingActionButton(localizations),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(AppLocalizations localizations) {
    return AppBar(
      title: Text(localizations.expenses),
      backgroundColor: context.colorScheme.surface,
      elevation: 0,
      actions: [
        if (_showForm)
          IconButton(
            icon: const Icon(Icons.close_rounded),
            onPressed: () {
              setState(() {
                _showForm = false;
              });
              _clearForm();
            },
            tooltip: 'Close Form',
          ),
        IconButton(
          icon: const Icon(Icons.analytics_rounded),
          onPressed: _showExpenseAnalytics,
          tooltip: 'View Analytics',
        ),
      ],
    );
  }

  Widget _buildExpenseForm(AppLocalizations localizations) {
    return Container(
      color: context.colorScheme.surface,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildFormHeader(localizations),
              const SizedBox(height: AppConstants.defaultPadding),
              CustomTextField(
                label: localizations.title,
                hint: 'Enter expense title',
                controller: _titleController,
                focusNode: _titleFocus,
                prefixIcon: Icons.title_rounded,
                validator: (value) => Validators.validateRequired(value, 'Title'),
                textInputAction: TextInputAction.next,
                onSubmitted: (_) => _amountFocus.requestFocus(),
              ),
              const SizedBox(height: AppConstants.defaultPadding),
              CustomTextField(
                label: localizations.amount,
                hint: 'Enter amount in ₹',
                controller: _amountController,
                focusNode: _amountFocus,
                keyboardType: TextInputType.number,
                prefixIcon: Icons.currency_rupee_rounded,
                validator: Validators.validateAmount,
                textInputAction: TextInputAction.next,
                onSubmitted: (_) => _descriptionFocus.requestFocus(),
              ),
              const SizedBox(height: AppConstants.defaultPadding),
              CustomTextField(
                label: localizations.description,
                hint: 'Enter expense description',
                controller: _descriptionController,
                focusNode: _descriptionFocus,
                prefixIcon: Icons.description_rounded,
                maxLines: 3,
                validator: Validators.validateDescription,
                textInputAction: TextInputAction.done,
              ),
              const SizedBox(height: AppConstants.defaultPadding),
              _buildDateSelector(localizations),
              const SizedBox(height: AppConstants.largePadding),
              Row(
                children: [
                  Expanded(
                    child: CustomButton(
                      text: localizations.cancel,
                      variant: ButtonVariant.secondary,
                      onPressed: () {
                        setState(() {
                          _showForm = false;
                        });
                        _clearForm();
                      },
                    ),
                  ),
                  const SizedBox(width: AppConstants.defaultPadding),
                  Expanded(
                    child: CustomButton(
                      text: 'Save Expense', // Fixed text to ensure visibility
                      onPressed: _saveExpense,
                      isLoading: _isLoading,
                      icon: Icons.save_rounded,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    ).animate().slideY(begin: -1, end: 0, duration: 400.ms, curve: Curves.easeOut);
  }

  Widget _buildFormHeader(AppLocalizations localizations) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: context.colorScheme.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            Icons.add_business_rounded,
            color: context.colorScheme.primary,
            size: 24,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Add New Expense',
                style: context.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                'Track your mandal expenses',
                style: context.textTheme.bodyMedium?.copyWith(
                  color: context.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDateSelector(AppLocalizations localizations) {
    return CustomCard(
      backgroundColor: context.colorScheme.surfaceVariant,
      child: Row(
        children: [
          Icon(
            Icons.calendar_today_rounded,
            color: context.colorScheme.primary,
          ),
          const SizedBox(width: 12),
          Text(
            '${localizations.date}: ${_selectedDate.toDisplayDate}',
            style: context.textTheme.bodyLarge,
          ),
          const Spacer(),
          CustomButton(
            text: 'Change',
            variant: ButtonVariant.text,
            size: ButtonSize.small,
            onPressed: _selectDate,
          ),
        ],
      ),
    );
  }

  Widget _buildExpensesList(AppLocalizations localizations) {
    return ValueListenableBuilder(
      valueListenable: HiveHelper.getExpensesBox().listenable(),
      builder: (context, Box<Expense> box, _) {
        final expenses = box.values.toList().cast<Expense>();
        
        if (expenses.isEmpty) {
          return _buildEmptyState(localizations);
        }

        // Calculate total
        final totalExpenses = expenses.fold<double>(
          0, (sum, expense) => sum + expense.amount,
        );

        return Column(
          children: [
            _buildExpensesSummary(totalExpenses, expenses.length, localizations),
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                itemCount: expenses.length,
                itemBuilder: (context, index) {
                  final expense = expenses[index];
                  return _buildExpenseCard(expense, index);
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildEmptyState(AppLocalizations localizations) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long_rounded,
            size: 64,
            color: context.colorScheme.onSurface.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 16),
          Text(
            localizations.noExpensesYet,
            style: context.textTheme.headlineSmall?.copyWith(
              color: context.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Track your mandal expenses here',
            style: context.textTheme.bodyMedium?.copyWith(
              color: context.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
          ),
          const SizedBox(height: 24),
          CustomButton(
            text: 'Add First Expense',
            onPressed: () {
              setState(() {
                _showForm = true;
              });
            },
            icon: Icons.add_rounded,
          ),
        ],
      ),
    );
  }

  Widget _buildExpenseCard(Expense expense, int index) {
    return CustomCard(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: context.colorScheme.errorContainer,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.money_off_rounded,
                  color: context.colorScheme.onErrorContainer,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      expense.title,
                      style: context.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      expense.date.toDisplayDate,
                      style: context.textTheme.bodySmall?.copyWith(
                        color: context.colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                  ],
                ),
              ),
              Text(
                expense.amount.toCurrencyCompact,
                style: context.textTheme.titleLarge?.copyWith(
                  color: context.colorScheme.error,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          if (expense.description.isNotEmpty) ...[
            const SizedBox(height: 12),
            Text(
              expense.description,
              style: context.textTheme.bodyMedium?.copyWith(
                color: context.colorScheme.onSurface.withValues(alpha: 0.8),
              ),
            ),
          ],
        ],
      ),
    ).animate(delay: (index * 100).ms).fadeIn().slideX(begin: -0.3, end: 0);
  }

  Widget _buildFloatingActionButton(AppLocalizations localizations) {
    return AnimatedBuilder(
      animation: _fabAnimationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _fabAnimationController.value,
          child: FloatingActionButton.extended(
            onPressed: () {
              setState(() {
                _showForm = !_showForm;
              });
              if (!_showForm) {
                _clearForm();
              }
            },
            icon: AnimatedRotation(
              turns: _showForm ? 0.125 : 0,
              duration: AppConstants.shortAnimation,
              child: Icon(_showForm ? Icons.close_rounded : Icons.add_rounded),
            ),
            label: Text(_showForm ? 'Cancel' : localizations.addExpense),
            backgroundColor: context.colorScheme.primary,
            foregroundColor: Colors.white,
          ),
        );
      },
    );
  }

  // Helper Methods
  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null) {
      setState(() {
        _selectedDate = date;
      });
    }
  }

  Future<void> _saveExpense() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        final expense = Expense(
          title: _titleController.text.trim(),
          amount: double.parse(_amountController.text),
          description: _descriptionController.text.trim(),
          date: _selectedDate,
        );

        await HiveHelper.getExpensesBox().add(expense);
        _clearForm();

        if (mounted) {
          setState(() {
            _showForm = false;
          });
          context.showSuccessSnackBar('Expense added successfully!');
        }
      } catch (e) {
        if (mounted) {
          context.showErrorSnackBar('Failed to add expense: ${e.toString()}');
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  void _clearForm() {
    _titleController.clear();
    _amountController.clear();
    _descriptionController.clear();
    _selectedDate = DateTime.now();
  }

  Widget _buildExpensesSummary(double total, int count, AppLocalizations localizations) {
    return Container(
      margin: const EdgeInsets.all(AppConstants.defaultPadding),
      child: CustomCard(
        backgroundColor: context.colorScheme.primaryContainer,
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Total Expenses',
                    style: context.textTheme.titleMedium?.copyWith(
                      color: context.colorScheme.onPrimaryContainer,
                    ),
                  ),
                  Text(
                    total.toCurrencyCompact,
                    style: context.textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: context.colorScheme.onPrimaryContainer,
                    ),
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: context.colorScheme.primary,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                '$count items',
                style: context.textTheme.bodySmall?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showExpenseAnalytics() {
    // TODO: Implement expense analytics
    context.showSnackBar('Analytics feature coming soon!');
  }
}
