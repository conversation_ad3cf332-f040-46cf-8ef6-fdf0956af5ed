import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:share_plus/share_plus.dart';
import 'package:wargani/core/constants/app_constants.dart';
import 'package:wargani/core/utils/extensions.dart';
import 'package:wargani/core/utils/validators.dart';
import 'package:wargani/l10n/app_localizations.dart';
import 'package:wargani/models/donation_model.dart';
import 'package:wargani/shared/widgets/custom_button.dart';
import 'package:wargani/shared/widgets/custom_card.dart';
import 'package:wargani/shared/widgets/custom_text_field.dart';
import 'package:wargani/shared/widgets/loading_overlay.dart';
import 'package:wargani/utils/hive_helper.dart';
import 'package:wargani/utils/pdf_generator.dart';
import 'package:hive_flutter/hive_flutter.dart';

/// Enhanced Donations Screen with professional UI
class EnhancedDonationsScreen extends StatefulWidget {
  const EnhancedDonationsScreen({super.key});

  @override
  State<EnhancedDonationsScreen> createState() => _EnhancedDonationsScreenState();
}

class _EnhancedDonationsScreenState extends State<EnhancedDonationsScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  
  // Controllers
  final _donorNameController = TextEditingController();
  final _amountController = TextEditingController();
  final _reasonController = TextEditingController();

  // Focus Nodes
  final _donorNameFocus = FocusNode();
  final _amountFocus = FocusNode();
  final _reasonFocus = FocusNode();

  DateTime _selectedDate = DateTime.now();
  bool _isLoading = false;
  bool _showForm = false;

  late AnimationController _animationController;
  late AnimationController _fabAnimationController;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: AppConstants.mediumAnimation,
      vsync: this,
    );
    _fabAnimationController = AnimationController(
      duration: AppConstants.shortAnimation,
      vsync: this,
    );
    _animationController.forward();
    _fabAnimationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _fabAnimationController.dispose();
    _donorNameController.dispose();
    _amountController.dispose();
    _reasonController.dispose();
    _donorNameFocus.dispose();
    _amountFocus.dispose();
    _reasonFocus.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    
    return LoadingOverlay(
      isLoading: _isLoading,
      message: 'Processing donation...',
      child: Scaffold(
        backgroundColor: context.colorScheme.background,
        appBar: _buildAppBar(localizations),
        body: Column(
          children: [
            if (_showForm) _buildDonationForm(localizations),
            Expanded(child: _buildDonationsList(localizations)),
          ],
        ),
        floatingActionButton: _buildFloatingActionButton(localizations),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(AppLocalizations localizations) {
    return AppBar(
      title: Text(localizations.donations),
      backgroundColor: context.colorScheme.surface,
      elevation: 0,
      actions: [
        if (_showForm)
          IconButton(
            icon: const Icon(Icons.close_rounded),
            onPressed: () {
              setState(() {
                _showForm = false;
              });
              _clearForm();
            },
            tooltip: 'Close Form',
          ),
        IconButton(
          icon: const Icon(Icons.analytics_rounded),
          onPressed: _showDonationAnalytics,
          tooltip: 'View Analytics',
        ),
      ],
    );
  }

  Widget _buildDonationForm(AppLocalizations localizations) {
    return Container(
      color: context.colorScheme.surface,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildFormHeader(localizations),
              const SizedBox(height: AppConstants.defaultPadding),
              CustomTextField(
                label: localizations.donorName,
                hint: 'Enter donor full name',
                controller: _donorNameController,
                focusNode: _donorNameFocus,
                prefixIcon: Icons.person_rounded,
                validator: Validators.validateName,
                textInputAction: TextInputAction.next,
                onSubmitted: (_) => _amountFocus.requestFocus(),
              ),
              const SizedBox(height: AppConstants.defaultPadding),
              CustomTextField(
                label: localizations.amount,
                hint: 'Enter donation amount in ₹',
                controller: _amountController,
                focusNode: _amountFocus,
                keyboardType: TextInputType.number,
                prefixIcon: Icons.currency_rupee_rounded,
                validator: Validators.validateAmount,
                textInputAction: TextInputAction.next,
                onSubmitted: (_) => _reasonFocus.requestFocus(),
              ),
              const SizedBox(height: AppConstants.defaultPadding),
              CustomTextField(
                label: localizations.reason,
                hint: 'Reason for donation (optional)',
                controller: _reasonController,
                focusNode: _reasonFocus,
                prefixIcon: Icons.note_rounded,
                maxLines: 2,
                textInputAction: TextInputAction.done,
              ),
              const SizedBox(height: AppConstants.defaultPadding),
              _buildDateSelector(localizations),
              const SizedBox(height: AppConstants.largePadding),
              Row(
                children: [
                  Expanded(
                    child: CustomButton(
                      text: localizations.cancel,
                      variant: ButtonVariant.secondary,
                      onPressed: () {
                        setState(() {
                          _showForm = false;
                        });
                        _clearForm();
                      },
                    ),
                  ),
                  const SizedBox(width: AppConstants.defaultPadding),
                  Expanded(
                    child: CustomButton(
                      text: 'Save Donation', // Fixed text to ensure visibility
                      onPressed: _saveDonation,
                      isLoading: _isLoading,
                      icon: Icons.save_rounded,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    ).animate().slideY(begin: -1, end: 0, duration: 400.ms, curve: Curves.easeOut);
  }

  Widget _buildFormHeader(AppLocalizations localizations) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.green.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Icon(
            Icons.volunteer_activism_rounded,
            color: Colors.green,
            size: 24,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Add New Donation',
                style: context.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                'Record donations received',
                style: context.textTheme.bodyMedium?.copyWith(
                  color: context.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDateSelector(AppLocalizations localizations) {
    return CustomCard(
      backgroundColor: context.colorScheme.surfaceVariant,
      child: Row(
        children: [
          Icon(
            Icons.calendar_today_rounded,
            color: context.colorScheme.primary,
          ),
          const SizedBox(width: 12),
          Text(
            '${localizations.date}: ${_selectedDate.toDisplayDate}',
            style: context.textTheme.bodyLarge,
          ),
          const Spacer(),
          CustomButton(
            text: 'Change',
            variant: ButtonVariant.text,
            size: ButtonSize.small,
            onPressed: _selectDate,
          ),
        ],
      ),
    );
  }

  Widget _buildDonationsList(AppLocalizations localizations) {
    return ValueListenableBuilder(
      valueListenable: HiveHelper.getDonationsBox().listenable(),
      builder: (context, Box<Donation> box, _) {
        final donations = box.values.toList().cast<Donation>();
        
        if (donations.isEmpty) {
          return _buildEmptyState(localizations);
        }

        // Calculate total
        final totalDonations = donations.fold<double>(
          0, (sum, donation) => sum + donation.amount,
        );

        return Column(
          children: [
            _buildDonationsSummary(totalDonations, donations.length, localizations),
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                itemCount: donations.length,
                itemBuilder: (context, index) {
                  final donation = donations[index];
                  return _buildDonationCard(donation, index);
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildDonationsSummary(double total, int count, AppLocalizations localizations) {
    return Container(
      margin: const EdgeInsets.all(AppConstants.defaultPadding),
      child: CustomCard(
        backgroundColor: Colors.green.withValues(alpha: 0.1),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Total Donations',
                    style: context.textTheme.titleMedium?.copyWith(
                      color: Colors.green.shade700,
                    ),
                  ),
                  Text(
                    total.toCurrencyCompact,
                    style: context.textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.green.shade700,
                    ),
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.green,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                '$count donors',
                style: context.textTheme.bodySmall?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(AppLocalizations localizations) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.volunteer_activism_rounded,
            size: 64,
            color: context.colorScheme.onSurface.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 16),
          Text(
            localizations.noDonationsYet,
            style: context.textTheme.headlineSmall?.copyWith(
              color: context.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Record donations received by your mandal',
            style: context.textTheme.bodyMedium?.copyWith(
              color: context.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
          ),
          const SizedBox(height: 24),
          CustomButton(
            text: 'Add First Donation',
            onPressed: () {
              setState(() {
                _showForm = true;
              });
            },
            icon: Icons.volunteer_activism_rounded,
          ),
        ],
      ),
    );
  }

  Widget _buildDonationCard(Donation donation, int index) {
    return CustomCard(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.volunteer_activism_rounded,
                  color: Colors.green,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      donation.donorName,
                      style: context.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      donation.date.toDisplayDate,
                      style: context.textTheme.bodySmall?.copyWith(
                        color: context.colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                  ],
                ),
              ),
              Text(
                donation.amount.toCurrencyCompact,
                style: context.textTheme.titleLarge?.copyWith(
                  color: Colors.green,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          if (donation.reason != null && donation.reason!.isNotEmpty) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: context.colorScheme.surfaceVariant,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.note_rounded,
                    size: 16,
                    color: context.colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      donation.reason!,
                      style: context.textTheme.bodyMedium?.copyWith(
                        color: context.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: CustomButton(
                  text: 'Download PDF',
                  variant: ButtonVariant.secondary,
                  size: ButtonSize.small,
                  icon: Icons.download_rounded,
                  onPressed: () => _downloadDonationPdf(donation),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: CustomButton(
                  text: 'Share',
                  size: ButtonSize.small,
                  icon: Icons.share_rounded,
                  onPressed: () => _shareDonationPdf(donation),
                ),
              ),
            ],
          ),
        ],
      ),
    ).animate(delay: (index * 100).ms).fadeIn().slideX(begin: 0.3, end: 0);
  }

  Widget _buildFloatingActionButton(AppLocalizations localizations) {
    return AnimatedBuilder(
      animation: _fabAnimationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _fabAnimationController.value,
          child: FloatingActionButton.extended(
            onPressed: () {
              setState(() {
                _showForm = !_showForm;
              });
              if (!_showForm) {
                _clearForm();
              }
            },
            icon: AnimatedRotation(
              turns: _showForm ? 0.125 : 0,
              duration: AppConstants.shortAnimation,
              child: Icon(_showForm ? Icons.close_rounded : Icons.add_rounded),
            ),
            label: Text(_showForm ? 'Cancel' : localizations.addDonation),
            backgroundColor: Colors.green,
            foregroundColor: Colors.white,
          ),
        );
      },
    );
  }

  // Helper Methods
  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null) {
      setState(() {
        _selectedDate = date;
      });
    }
  }

  Future<void> _saveDonation() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        final donation = Donation(
          donorName: _donorNameController.text.trim(),
          amount: double.parse(_amountController.text),
          reason: _reasonController.text.trim().isNotEmpty
              ? _reasonController.text.trim()
              : null,
          date: _selectedDate,
        );

        await HiveHelper.getDonationsBox().add(donation);
        _clearForm();

        if (mounted) {
          setState(() {
            _showForm = false;
          });
          context.showSuccessSnackBar('Donation recorded successfully!');
        }
      } catch (e) {
        if (mounted) {
          context.showErrorSnackBar('Failed to record donation: ${e.toString()}');
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  void _clearForm() {
    _donorNameController.clear();
    _amountController.clear();
    _reasonController.clear();
    _selectedDate = DateTime.now();
  }

  void _showDonationAnalytics() {
    // TODO: Implement donation analytics
    context.showSnackBar('Analytics feature coming soon!');
  }

  Future<void> _downloadDonationPdf(Donation donation) async {
    try {
      setState(() {
        _isLoading = true;
      });

      final user = HiveHelper.getUsersBox().isNotEmpty
          ? HiveHelper.getUsersBox().getAt(0)
          : null;

      final pdfPath = await PdfGenerator.generateDonationReceipt(
        context,
        donation,
        user?.name,
      );

      if (pdfPath != null && mounted) {
        await Share.shareXFiles([XFile(pdfPath)]);
        if (mounted) {
          context.showSuccessSnackBar('Donation receipt downloaded successfully!');
        }
      }
    } catch (e) {
      if (mounted) {
        context.showErrorSnackBar('Failed to generate PDF: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _shareDonationPdf(Donation donation) async {
    try {
      setState(() {
        _isLoading = true;
      });

      final user = HiveHelper.getUsersBox().isNotEmpty
          ? HiveHelper.getUsersBox().getAt(0)
          : null;

      final pdfPath = await PdfGenerator.generateDonationReceipt(
        context,
        donation,
        user?.name,
      );

      if (pdfPath != null && mounted) {
        await Share.shareXFiles(
          [XFile(pdfPath)],
          text: 'Thank you ${donation.donorName} for your generous donation of ₹${donation.amount}!',
        );
        if (mounted) {
          context.showSuccessSnackBar('Donation receipt shared successfully!');
        }
      }
    } catch (e) {
      if (mounted) {
        context.showErrorSnackBar('Failed to share PDF: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
