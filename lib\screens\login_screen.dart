import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:wargani/core/constants/app_constants.dart';
import 'package:wargani/core/utils/extensions.dart';
import 'package:wargani/core/utils/validators.dart';
import 'package:wargani/l10n/app_localizations.dart';
import 'package:wargani/models/user_model.dart';
import 'package:wargani/screens/home_screen.dart';
import 'package:wargani/screens/signup_screen.dart';
import 'package:wargani/screens/forgot_password_screen.dart';
import 'package:wargani/shared/widgets/custom_button.dart';
import 'package:wargani/shared/widgets/custom_text_field.dart';
import 'package:wargani/shared/widgets/loading_overlay.dart';
import 'package:wargani/utils/hive_helper.dart';

/// Enhanced professional login screen
class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _usernameFocusNode = FocusNode();
  final _emailFocusNode = FocusNode();
  final _passwordFocusNode = FocusNode();

  bool _isLoading = false;
  bool _obscurePassword = true;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.2, 1.0, curve: Curves.easeOutCubic),
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _emailFocusNode.dispose();
    _passwordFocusNode.dispose();
    super.dispose();
  }

  Future<void> _login() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        final userBox = HiveHelper.getUsersBox();
        final users = userBox.values.where((user) =>
            user.name == _usernameController.text &&
            user.password == _passwordController.text);

        if (users.isNotEmpty) {
          // Success - navigate to home
          if (mounted) {
            Navigator.pushReplacement(
              context,
              PageRouteBuilder(
                pageBuilder: (context, animation, secondaryAnimation) =>
                    const HomeScreen(),
                transitionsBuilder: (context, animation, secondaryAnimation, child) {
                  return FadeTransition(opacity: animation, child: child);
                },
                transitionDuration: const Duration(milliseconds: 300),
              ),
            );
          }
        } else {
          // Failed login
          final allUsers = userBox.values;
          final message = allUsers.isEmpty
              ? 'No users registered. Please sign up.'
              : 'Invalid email or password';

          if (mounted) {
            context.showErrorSnackBar(message);
          }
        }
      } catch (e) {
        if (mounted) {
          context.showErrorSnackBar('Login failed: ${e.toString()}');
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return LoadingOverlay(
      isLoading: _isLoading,
      message: 'Logging in...',
      child: Scaffold(
        backgroundColor: context.colorScheme.surface,
        body: SafeArea(
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: _buildLoginForm(localizations),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildLoginForm(AppLocalizations localizations) {
    return Center(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildHeader(localizations),
              const SizedBox(height: 48),
              _buildUsernameField(localizations),
              const SizedBox(height: 20),
              _buildPasswordField(localizations),
              const SizedBox(height: 32),
              _buildLoginButton(localizations),
              const SizedBox(height: 16),
              _buildForgotPasswordButton(localizations),
              const SizedBox(height: 24),
              _buildSignUpButton(localizations),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(AppLocalizations localizations) {
    return Column(
      children: [
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                context.colorScheme.primary,
                context.colorScheme.primary.withValues(alpha: 0.8),
              ],
            ),
            borderRadius: BorderRadius.circular(20),
          ),
          child: const Icon(
            Icons.temple_hindu_rounded,
            size: 40,
            color: Colors.white,
          ),
        ).animate().scale(delay: 200.ms, duration: 600.ms, curve: Curves.elasticOut),
        const SizedBox(height: 24),
        Text(
          'Welcome Wargani App',
          style: context.textTheme.displaySmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: context.colorScheme.onSurface,
          ),
        ).animate().fadeIn(delay: 400.ms).slideY(begin: 0.3, end: 0),
        const SizedBox(height: 8),
        Text(
          '|| श्री गणपतये नमः ||',
          style: context.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
            color: Colors.orange.shade700,
          ),
          textAlign: TextAlign.center,
        ).animate().fadeIn(delay: 500.ms),
        const SizedBox(height: 8),
        Text(
          localizations.developedBy,
          style: context.textTheme.bodyMedium?.copyWith(
            color: context.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
          textAlign: TextAlign.center,
        ).animate().fadeIn(delay: 600.ms),
      ],
    );
  }

  Widget _buildUsernameField(AppLocalizations localizations) {
    return CustomTextField(
      label: 'Username',
      hint: 'Enter your username',
      controller: _usernameController,
      focusNode: _usernameFocusNode,
      keyboardType: TextInputType.text,
      prefixIcon: Icons.person_rounded,
      validator: (value) => value?.isEmpty == true ? 'Username is required' : null,
      textInputAction: TextInputAction.next,
      onSubmitted: (_) => _passwordFocusNode.requestFocus(),
    ).animate().fadeIn(delay: 700.ms).slideX(begin: -0.3, end: 0);
  }

  Widget _buildPasswordField(AppLocalizations localizations) {
    return CustomTextField(
      label: localizations.password,
      hint: 'Enter your password',
      controller: _passwordController,
      focusNode: _passwordFocusNode,
      obscureText: _obscurePassword,
      prefixIcon: Icons.lock_rounded,
      suffixIcon: IconButton(
        icon: Icon(
          _obscurePassword ? Icons.visibility_rounded : Icons.visibility_off_rounded,
        ),
        onPressed: () {
          setState(() {
            _obscurePassword = !_obscurePassword;
          });
        },
      ),
      validator: Validators.validatePassword,
      textInputAction: TextInputAction.done,
      onSubmitted: (_) => _login(),
    ).animate().fadeIn(delay: 1000.ms).slideX(begin: 0.3, end: 0);
  }

  Widget _buildLoginButton(AppLocalizations localizations) {
    return CustomButton(
      text: localizations.login,
      onPressed: _login,
      isLoading: _isLoading,
      isFullWidth: true,
      size: ButtonSize.large,
      icon: Icons.login_rounded,
    ).animate().fadeIn(delay: 1200.ms).slideY(begin: 0.3, end: 0);
  }

  Widget _buildForgotPasswordButton(AppLocalizations localizations) {
    return CustomButton(
      text: localizations.forgotPassword,
      variant: ButtonVariant.text,
      onPressed: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const ForgotPasswordScreen(),
          ),
        );
      },
    ).animate().fadeIn(delay: 1400.ms);
  }

  Widget _buildSignUpButton(AppLocalizations localizations) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          "Don't have an account? ",
          style: context.textTheme.bodyMedium,
        ),
        CustomButton(
          text: 'Sign Up',
          variant: ButtonVariant.text,
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const SignUpScreen(),
              ),
            );
          },
        ),
      ],
    ).animate().fadeIn(delay: 1600.ms);
  }
}
