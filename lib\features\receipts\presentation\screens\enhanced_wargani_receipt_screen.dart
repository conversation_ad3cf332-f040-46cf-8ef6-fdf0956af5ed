import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:intl/intl.dart';
import 'package:wargani/core/constants/app_constants.dart';
import 'package:wargani/core/utils/extensions.dart';
import 'package:wargani/core/utils/validators.dart';
import 'package:wargani/l10n/app_localizations.dart';
import 'package:wargani/models/wargani_model.dart';
import 'package:wargani/shared/widgets/custom_button.dart';
import 'package:wargani/shared/widgets/custom_card.dart';
import 'package:wargani/shared/widgets/custom_text_field.dart';
import 'package:wargani/shared/widgets/loading_overlay.dart';
import 'package:wargani/utils/hive_helper.dart';
import 'package:wargani/utils/pdf_generator.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';

/// Enhanced Wargani Receipt Screen with professional UI
class EnhancedWarganiReceiptScreen extends StatefulWidget {
  const EnhancedWarganiReceiptScreen({super.key});

  @override
  State<EnhancedWarganiReceiptScreen> createState() => _EnhancedWarganiReceiptScreenState();
}

class _EnhancedWarganiReceiptScreenState extends State<EnhancedWarganiReceiptScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _pageController = PageController();
  
  // Controllers
  final _receiptNoController = TextEditingController();
  final _nameController = TextEditingController();
  final _amountController = TextEditingController();
  final _prefixController = TextEditingController();
  final _mobileNoController = TextEditingController();
  final _registrationNoController = TextEditingController();
  final _amountInWordsController = TextEditingController();

  // Focus Nodes
  final _receiptNoFocus = FocusNode();
  final _nameFocus = FocusNode();
  final _amountFocus = FocusNode();
  final _prefixFocus = FocusNode();
  final _mobileFocus = FocusNode();
  final _registrationFocus = FocusNode();
  final _amountWordsFocus = FocusNode();

  DateTime _selectedDate = DateTime.now();
  bool _isLoading = false;
  int _currentPageIndex = 0;

  late AnimationController _animationController;
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _setupControllers();
    _loadNextReceiptNumber();
    _loadProfileData();
  }

  void _setupControllers() {
    _animationController = AnimationController(
      duration: AppConstants.mediumAnimation,
      vsync: this,
    );
    _tabController = TabController(length: 2, vsync: this);
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _tabController.dispose();
    _pageController.dispose();
    
    // Dispose controllers
    _receiptNoController.dispose();
    _nameController.dispose();
    _amountController.dispose();
    _prefixController.dispose();
    _mobileNoController.dispose();
    _registrationNoController.dispose();
    _amountInWordsController.dispose();
    
    // Dispose focus nodes
    _receiptNoFocus.dispose();
    _nameFocus.dispose();
    _amountFocus.dispose();
    _prefixFocus.dispose();
    _mobileFocus.dispose();
    _registrationFocus.dispose();
    _amountWordsFocus.dispose();
    
    super.dispose();
  }

  void _loadNextReceiptNumber() {
    final warganiBox = HiveHelper.getWarganiBox();
    final nextReceiptNo = warganiBox.length + 1;
    _receiptNoController.text = nextReceiptNo.toString();
  }

  void _loadProfileData() {
    final profileBox = HiveHelper.getProfileBox();
    if (profileBox.isNotEmpty) {
      final profile = profileBox.getAt(0);
      if (profile?.mandalRegistrationNo != null) {
        _registrationNoController.text = profile!.mandalRegistrationNo!;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    
    return LoadingOverlay(
      isLoading: _isLoading,
      message: 'Processing receipt...',
      child: Scaffold(
        backgroundColor: context.colorScheme.background,
        appBar: _buildAppBar(localizations),
        body: Column(
          children: [
            _buildTabBar(localizations),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildReceiptForm(localizations),
                  _buildReceiptsList(localizations),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(AppLocalizations localizations) {
    return AppBar(
      title: Text(localizations.warganiReceipt),
      backgroundColor: context.colorScheme.surface,
      elevation: 0,
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh_rounded),
          onPressed: _loadNextReceiptNumber,
          tooltip: 'Refresh Receipt Number',
        ),
      ],
    );
  }

  Widget _buildTabBar(AppLocalizations localizations) {
    return Container(
      color: context.colorScheme.surface,
      child: TabBar(
        controller: _tabController,
        tabs: [
          Tab(
            icon: const Icon(Icons.add_rounded),
            text: 'New Receipt',
          ),
          Tab(
            icon: const Icon(Icons.list_rounded),
            text: 'All Receipts',
          ),
        ],
        labelColor: context.colorScheme.primary,
        unselectedLabelColor: context.colorScheme.onSurface.withValues(alpha: 0.6),
        indicatorColor: context.colorScheme.primary,
      ),
    );
  }

  Widget _buildReceiptForm(AppLocalizations localizations) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildFormHeader(localizations),
            const SizedBox(height: AppConstants.largePadding),
            _buildBasicInfoSection(localizations),
            const SizedBox(height: AppConstants.largePadding),
            _buildContactInfoSection(localizations),
            const SizedBox(height: AppConstants.largePadding),
            _buildAdditionalInfoSection(localizations),
            const SizedBox(height: AppConstants.largePadding),
            _buildActionButtons(localizations),
            const SizedBox(height: AppConstants.largePadding),
          ],
        ),
      ),
    ).animate().fadeIn().slideY(begin: 0.3, end: 0);
  }

  Widget _buildFormHeader(AppLocalizations localizations) {
    return CustomCard(
      backgroundColor: context.colorScheme.primaryContainer,
      child: Column(
        children: [
          Icon(
            Icons.receipt_long_rounded,
            size: 48,
            color: context.colorScheme.primary,
          ),
          const SizedBox(height: 12),
          Text(
            'Create New Receipt',
            style: context.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: context.colorScheme.onPrimaryContainer,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Fill in the details to generate a professional receipt',
            style: context.textTheme.bodyMedium?.copyWith(
              color: context.colorScheme.onPrimaryContainer.withValues(alpha: 0.8),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBasicInfoSection(AppLocalizations localizations) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Basic Information',
            style: context.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Row(
            children: [
              Expanded(
                flex: 1,
                child: CustomTextField(
                  label: localizations.receiptNo,
                  controller: _receiptNoController,
                  focusNode: _receiptNoFocus,
                  keyboardType: TextInputType.number,
                  prefixIcon: Icons.numbers_rounded,
                  validator: Validators.validateReceiptNumber,
                  textInputAction: TextInputAction.next,
                  onSubmitted: (_) => _prefixFocus.requestFocus(),
                ),
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              Expanded(
                flex: 2,
                child: CustomTextField(
                  label: localizations.prefix,
                  hint: 'Mr./Mrs./Dr.',
                  controller: _prefixController,
                  focusNode: _prefixFocus,
                  prefixIcon: Icons.person_outline_rounded,
                  validator: (value) => Validators.validateRequired(value, 'Prefix'),
                  textInputAction: TextInputAction.next,
                  onSubmitted: (_) => _nameFocus.requestFocus(),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          CustomTextField(
            label: localizations.name,
            hint: 'Enter full name',
            controller: _nameController,
            focusNode: _nameFocus,
            prefixIcon: Icons.person_rounded,
            validator: Validators.validateName,
            textInputAction: TextInputAction.next,
            onSubmitted: (_) => _amountFocus.requestFocus(),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          CustomTextField(
            label: localizations.amount,
            hint: 'Enter amount in ₹',
            controller: _amountController,
            focusNode: _amountFocus,
            keyboardType: TextInputType.number,
            prefixIcon: Icons.currency_rupee_rounded,
            validator: Validators.validateAmount,
            textInputAction: TextInputAction.next,
            onSubmitted: (_) => _amountWordsFocus.requestFocus(),
            onChanged: _onAmountChanged,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          CustomTextField(
            label: localizations.amountInWords,
            hint: 'Amount in words',
            controller: _amountInWordsController,
            focusNode: _amountWordsFocus,
            prefixIcon: Icons.text_fields_rounded,
            validator: (value) => Validators.validateRequired(value, 'Amount in words'),
            textInputAction: TextInputAction.next,
            onSubmitted: (_) => _mobileFocus.requestFocus(),
          ),
        ],
      ),
    );
  }

  Widget _buildContactInfoSection(AppLocalizations localizations) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Contact Information',
            style: context.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          CustomTextField(
            label: localizations.mobileNo,
            hint: 'Enter 10-digit mobile number',
            controller: _mobileNoController,
            focusNode: _mobileFocus,
            keyboardType: TextInputType.phone,
            prefixIcon: Icons.phone_rounded,
            validator: Validators.validateMobileNumber,
            textInputAction: TextInputAction.next,
            onSubmitted: (_) => _registrationFocus.requestFocus(),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          CustomTextField(
            label: localizations.registrationNo,
            hint: 'Mandal registration number',
            controller: _registrationNoController,
            focusNode: _registrationFocus,
            prefixIcon: Icons.badge_rounded,
            textInputAction: TextInputAction.done,
          ),
        ],
      ),
    );
  }

  Widget _buildAdditionalInfoSection(AppLocalizations localizations) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Additional Information',
            style: context.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Row(
            children: [
              Icon(
                Icons.calendar_today_rounded,
                color: context.colorScheme.primary,
              ),
              const SizedBox(width: 12),
              Text(
                '${localizations.date}: ${_selectedDate.toDisplayDate}',
                style: context.textTheme.bodyLarge,
              ),
              const Spacer(),
              CustomButton(
                text: 'Change Date',
                variant: ButtonVariant.secondary,
                size: ButtonSize.small,
                onPressed: _selectDate,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(AppLocalizations localizations) {
    return Column(
      children: [
        CustomButton(
          text: localizations.saveReceipt,
          onPressed: _saveReceipt,
          isLoading: _isLoading,
          isFullWidth: true,
          size: ButtonSize.large,
          icon: Icons.save_rounded,
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        Row(
          children: [
            Expanded(
              child: CustomButton(
                text: localizations.clearForm,
                variant: ButtonVariant.secondary,
                onPressed: _clearForm,
                icon: Icons.clear_rounded,
              ),
            ),
            const SizedBox(width: AppConstants.defaultPadding),
            Expanded(
              child: CustomButton(
                text: localizations.preview,
                variant: ButtonVariant.secondary,
                onPressed: _previewReceipt,
                icon: Icons.preview_rounded,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildReceiptsList(AppLocalizations localizations) {
    return ValueListenableBuilder(
      valueListenable: HiveHelper.getWarganiBox().listenable(),
      builder: (context, Box<Wargani> box, _) {
        final receipts = box.values.toList().cast<Wargani>();

        if (receipts.isEmpty) {
          return _buildEmptyState(localizations);
        }

        return ListView.builder(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          itemCount: receipts.length,
          itemBuilder: (context, index) {
            final receipt = receipts[index];
            return _buildReceiptCard(receipt, localizations, index);
          },
        );
      },
    );
  }

  Widget _buildEmptyState(AppLocalizations localizations) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long_rounded,
            size: 64,
            color: context.colorScheme.onSurface.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 16),
          Text(
            'No receipts yet',
            style: context.textTheme.headlineSmall?.copyWith(
              color: context.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Create your first receipt to get started',
            style: context.textTheme.bodyMedium?.copyWith(
              color: context.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReceiptCard(Wargani receipt, AppLocalizations localizations, int index) {
    return CustomCard(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: context.colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '#${receipt.receiptNo}',
                  style: context.textTheme.labelLarge?.copyWith(
                    color: context.colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const Spacer(),
              Text(
                receipt.date.toDisplayDate,
                style: context.textTheme.bodySmall?.copyWith(
                  color: context.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            '${receipt.prefix} ${receipt.name}',
            style: context.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            receipt.amount.toCurrencyCompact,
            style: context.textTheme.headlineSmall?.copyWith(
              color: context.colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
          if (receipt.mobileNo != null && receipt.mobileNo!.isNotEmpty) ...[
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  Icons.phone_rounded,
                  size: 16,
                  color: context.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
                const SizedBox(width: 4),
                Text(
                  receipt.mobileNo!,
                  style: context.textTheme.bodySmall?.copyWith(
                    color: context.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          ],
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: CustomButton(
                  text: localizations.download,
                  variant: ButtonVariant.secondary,
                  size: ButtonSize.small,
                  icon: Icons.download_rounded,
                  onPressed: () => _downloadPdf(receipt),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: CustomButton(
                  text: 'WhatsApp Share',
                  size: ButtonSize.small,
                  icon: Icons.share_rounded,
                  onPressed: () => _sharePdf(receipt),
                ),
              ),
            ],
          ),
        ],
      ),
    ).animate(delay: (index * 100).ms).fadeIn().slideX(begin: 0.3, end: 0);
  }

  // Helper Methods
  void _onAmountChanged(String value) {
    if (value.isNotEmpty) {
      final amount = double.tryParse(value);
      if (amount != null) {
        // Auto-generate amount in words (simplified)
        _amountInWordsController.text = _numberToWords(amount);
      }
    }
  }

  String _numberToWords(double amount) {
    // Simplified number to words conversion
    final intAmount = amount.toInt();
    if (intAmount < 1000) {
      return '$intAmount Rupees Only';
    } else if (intAmount < 100000) {
      final thousands = intAmount ~/ 1000;
      final remainder = intAmount % 1000;
      return remainder == 0
          ? '$thousands Thousand Rupees Only'
          : '$thousands Thousand $remainder Rupees Only';
    } else {
      return '$intAmount Rupees Only';
    }
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null) {
      setState(() {
        _selectedDate = date;
      });
    }
  }

  Future<void> _saveReceipt() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        final wargani = Wargani(
          receiptNo: int.parse(_receiptNoController.text),
          name: _nameController.text.trim(),
          amount: double.parse(_amountController.text),
          date: _selectedDate,
          prefix: _prefixController.text.trim(),
          mobileNo: _mobileNoController.text.trim().isNotEmpty
              ? _mobileNoController.text.trim()
              : null,
          registrationNo: _registrationNoController.text.trim(),
          amountInWords: _amountInWordsController.text.trim(),
        );

        await HiveHelper.getWarganiBox().add(wargani);
        _clearForm();
        _loadNextReceiptNumber();

        if (mounted) {
          context.showSuccessSnackBar('Receipt saved successfully!');
          _showSaveSuccessDialog(wargani);
        }
      } catch (e) {
        if (mounted) {
          context.showErrorSnackBar('Failed to save receipt: ${e.toString()}');
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  void _showSaveSuccessDialog(Wargani wargani) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Receipt Saved Successfully!'),
        content: const Text('What would you like to do next?'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _tabController.animateTo(1); // Go to receipts list
            },
            child: const Text('View Receipts'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _downloadPdf(wargani);
            },
            child: const Text('Download PDF'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _sharePdf(wargani);
            },
            child: const Text('WhatsApp Share'),
          ),
        ],
      ),
    );
  }

  void _clearForm() {
    _nameController.clear();
    _amountController.clear();
    _prefixController.clear();
    _mobileNoController.clear();
    _registrationNoController.clear();
    _amountInWordsController.clear();
    _selectedDate = DateTime.now();
    setState(() {});
  }

  Future<void> _previewReceipt() async {
    if (_formKey.currentState!.validate()) {
      // Create temporary Wargani object for preview
      final tempWargani = Wargani(
        receiptNo: int.parse(_receiptNoController.text),
        name: _nameController.text.trim(),
        amount: double.parse(_amountController.text),
        date: _selectedDate,
        prefix: _prefixController.text.trim(),
        mobileNo: _mobileNoController.text.trim().isNotEmpty
            ? _mobileNoController.text.trim()
            : null,
        registrationNo: _registrationNoController.text.trim(),
        amountInWords: _amountInWordsController.text.trim(),
      );

      // Show preview dialog
      _showPreviewDialog(tempWargani);
    }
  }

  void _showPreviewDialog(Wargani wargani) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Receipt Preview'),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildPreviewItem('Name', wargani.name),
              _buildPreviewItem('Amount', '₹${wargani.amount}'),
              _buildPreviewItem('Amount in Words', wargani.amountInWords ?? ''),
              _buildPreviewItem('Date', DateFormat('dd/MM/yyyy').format(wargani.date)),
              if (wargani.mobileNo != null)
                _buildPreviewItem('Mobile', wargani.mobileNo!),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _downloadPreviewPdf(wargani);
            },
            child: const Text('Download PDF'),
          ),
        ],
      ),
    );
  }

  Widget _buildPreviewItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  Future<void> _downloadPreviewPdf(Wargani wargani) async {
    try {
      setState(() {
        _isLoading = true;
      });

      final user = HiveHelper.getUsersBox().isNotEmpty
          ? HiveHelper.getUsersBox().getAt(0)
          : null;

      final pdfPath = await PdfGenerator.generateProfessionalWarganiReceipt(
        context,
        wargani,
        user?.name,
      );

      if (pdfPath != null && mounted) {
        await Share.shareXFiles([XFile(pdfPath)]);
        if (mounted) {
          context.showSuccessSnackBar('PDF generated and ready to share!');
        }
      }
    } catch (e) {
      if (mounted) {
        context.showErrorSnackBar('Failed to generate PDF: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _downloadPdf(Wargani wargani) async {
    try {
      setState(() {
        _isLoading = true;
      });

      final pdfPath = await PdfGenerator.generateProfessionalWarganiReceipt(
        context,
        wargani,
        HiveHelper.getUsersBox().getAt(0)?.name,
      );

      if (pdfPath != null) {
        await Share.shareXFiles([XFile(pdfPath)]);
        if (mounted) {
          context.showSuccessSnackBar('PDF generated successfully!');
        }
      }
    } catch (e) {
      if (mounted) {
        context.showErrorSnackBar('Failed to generate PDF: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _sharePdf(Wargani wargani) async {
    try {
      setState(() {
        _isLoading = true;
      });

      final pdfPath = await PdfGenerator.generateProfessionalWarganiReceipt(
        context,
        wargani,
        HiveHelper.getUsersBox().getAt(0)?.name,
      );

      if (pdfPath != null && mounted) {
        if (wargani.mobileNo != null && wargani.mobileNo!.isNotEmpty) {
          // Share via WhatsApp with PDF attachment
          await Share.shareXFiles(
            [XFile(pdfPath)],
            text: 'धन्यवाद! आपल्या ${wargani.amount} रुपयांच्या वरगणीसाठी. 🙏\n\nThank you for your generous contribution of ₹${wargani.amount} to our Ganesh Mandal! 🕉️',
          );

          // Also try to open WhatsApp directly
          final url = "https://wa.me/${wargani.mobileNo}?text=धन्यवाद! आपल्या वरगणीसाठी 🙏";
          if (await canLaunchUrl(Uri.parse(url))) {
            await launchUrl(Uri.parse(url));
          }
        } else {
          // Regular share without WhatsApp
          await Share.shareXFiles([XFile(pdfPath)]);
        }

        if (mounted) {
          context.showSuccessSnackBar('Receipt shared successfully!');
        }
      }
    } catch (e) {
      if (mounted) {
        context.showErrorSnackBar('Failed to share receipt: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
